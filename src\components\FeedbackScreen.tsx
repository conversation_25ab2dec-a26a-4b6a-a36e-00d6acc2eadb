import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/utils/toast';
import PopupScreen from '@/components/ui/popup-screen';
import { getDeviceId } from '../services/device';
import { getApiUrl } from '../config/api';

interface FeedbackScreenProps {
  onClose: () => void;
}

const FeedbackScreen: React.FC<FeedbackScreenProps> = ({ onClose }) => {
  const [feedbackType, setFeedbackType] = useState('suggestion');
  const [feedbackEmail, setFeedbackEmail] = useState('');
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!feedbackMessage.trim()) {
      toast.error('Please enter your feedback message');
      return;
    }

    setIsSubmitting(true);

    try {
      const userId = await getDeviceId();
      const backendUrl = `${getApiUrl()}/feedback`;

      console.log(`Sending feedback to: ${backendUrl}`); // Added for debugging

      const response = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          feedbackType,
          email: feedbackEmail || null,
          message: feedbackMessage,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to submit feedback. Please try again.' }));
        throw new Error(errorData.error || errorData.message || `HTTP error! status: ${response.status}`);
      }

      toast.feedbackSuccess();

      // Reset form and close after a short delay
      setFeedbackType('suggestion');
      setFeedbackEmail('');
      setFeedbackMessage('');
      
      setTimeout(() => {
        onClose();
      }, 1000);
    } catch (error) {
      let errorMessage = 'An unknown error occurred.';
      if (error instanceof Error) {
        errorMessage = error.message;
        console.error('Feedback submission error (Error instance):', error);
      } else {
        // Attempt to stringify for more details if it's not a standard Error
        try {
          errorMessage = JSON.stringify(error);
        } catch (e) {
          // If stringify fails, use a generic message
          errorMessage = 'Non-standard error object received and could not be stringified.';
        }
        console.error('Feedback submission error (Non-Error instance):', error, 'Stringified:', errorMessage);
      }
      toast.error(`Failed to send feedback: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PopupScreen title="Send Feedback" onClose={onClose}>
      <div className="grid gap-2 max-w-md mx-auto prevent-overflow">
        <div className="grid gap-1">
          <Label htmlFor="feedback-type" className="text-xs">Feedback Type</Label>
          <Select
            value={feedbackType}
            onValueChange={setFeedbackType}
          >
            <SelectTrigger id="feedback-type" className="h-8 text-xs user-select-text" style={{ touchAction: 'manipulation' }}>
              <SelectValue placeholder="Select feedback type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="suggestion">Suggestion</SelectItem>
              <SelectItem value="bug">Bug Report</SelectItem>
              <SelectItem value="question">Question</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-1">
          <Label htmlFor="email" className="text-xs">Email (optional)</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={feedbackEmail}
            onChange={(e) => setFeedbackEmail(e.target.value)}
            className="h-8 text-xs user-select-text"
            style={{ touchAction: 'manipulation' }}
          />
        </div>

        <div className="grid gap-1">
          <Label htmlFor="message" className="text-xs">Your Feedback</Label>
          <Textarea
            id="message"
            placeholder="Tell us what you think..."
            rows={3}
            value={feedbackMessage}
            onChange={(e) => setFeedbackMessage(e.target.value)}
            className="min-h-[70px] text-xs user-select-text"
            style={{ touchAction: 'manipulation' }}
          />
        </div>

        <Button
          className="w-full mt-1 h-8 text-xs"
          onClick={handleSubmit}
          disabled={isSubmitting}
          style={{ touchAction: 'manipulation' }}
        >
          {isSubmitting ? 'Sending...' : 'Send Feedback'}
        </Button>
      </div>
    </PopupScreen>
  );
};

export default FeedbackScreen;
