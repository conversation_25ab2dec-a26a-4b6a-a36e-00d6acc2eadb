import { Preferences } from '@capacitor/preferences';

/**
 * Utility functions for chat text hashing and duplicate detection
 */

export interface ChatHashEntry {
  hash: string;
  analysisId: string;
  createdAt: number;
  firstTenLines: string; // For debugging/validation
}

// Storage key for hash mappings
const HASH_STORAGE_KEY = 'chat-vibe-analyzer-hashes';

/**
 * Extract the first 10 lines from chat text
 */
export function extractFirstTenLines(chatText: string): string {
  if (!chatText || typeof chatText !== 'string') {
    return '';
  }

  const lines = chatText
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0); // Remove empty lines

  // Get first 10 non-empty lines
  const firstTenLines = lines.slice(0, 10);
  
  return firstTenLines.join('\n');
}

/**
 * Simple hash function fallback for environments without crypto.subtle
 */
function simpleHash(str: string): string {
  let hash = 0;
  if (str.length === 0) return '0';
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  // Convert to positive hex string with padding
  return Math.abs(hash).toString(16).padStart(8, '0');
}

/**
 * Generate SHA-256 hash of the first 10 lines with crypto.subtle fallback
 */
export async function generateChatHash(chatText: string): Promise<string> {
  const firstTenLines = extractFirstTenLines(chatText);
  
  if (!firstTenLines) {
    // Return a default hash for empty/invalid text
    return 'empty-chat-hash';
  }

  // Normalize the text (lowercase, trim) to handle minor variations
  const normalizedText = firstTenLines.toLowerCase().trim();
  
  // Try to use crypto.subtle if available
  if (typeof crypto !== 'undefined' && crypto.subtle && crypto.subtle.digest) {
    try {
      console.log('⚡️  [log] - Using crypto.subtle for hash generation');
      // Generate SHA-256 hash
      const encoder = new TextEncoder();
      const data = encoder.encode(normalizedText);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      
      // Convert to hex string
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      
      return hashHex;
    } catch (error) {
      console.warn('⚡️  [log] - crypto.subtle failed, falling back to simple hash:', error);
    }
  }
  
  // Fallback to simple hash for mobile/Capacitor environments
  console.log('⚡️  [log] - Using simple hash fallback for mobile environment');
  const simpleHashValue = simpleHash(normalizedText);
  
  // Add a prefix to distinguish from crypto hashes and include timestamp for uniqueness
  const timestamp = Date.now().toString(16).slice(-6); // Last 6 hex digits of timestamp
  return `simple-${simpleHashValue}-${timestamp}`;
}

/**
 * Get all stored chat hashes
 */
export async function getStoredHashes(): Promise<ChatHashEntry[]> {
  try {
    const result = await Preferences.get({ key: HASH_STORAGE_KEY });
    if (result.value) {
      return JSON.parse(result.value);
    }
  } catch (error) {
    console.error('Error retrieving stored hashes:', error);
  }
  return [];
}

/**
 * Store a new chat hash
 */
export async function storeChatHash(
  hash: string,
  analysisId: string,
  firstTenLines: string
): Promise<void> {
  try {
    const existingHashes = await getStoredHashes();
    
    const newHashEntry: ChatHashEntry = {
      hash,
      analysisId,
      createdAt: Date.now(),
      firstTenLines
    };
    
    // Add to existing hashes (keep all entries, even duplicates)
    const updatedHashes = [...existingHashes, newHashEntry];
    
    await Preferences.set({
      key: HASH_STORAGE_KEY,
      value: JSON.stringify(updatedHashes)
    });
  } catch (error) {
    console.error('Error storing chat hash:', error);
    throw error;
  }
}

/**
 * Find existing analysis by chat hash
 */
export async function findAnalysisByHash(hash: string): Promise<ChatHashEntry | null> {
  try {
    const storedHashes = await getStoredHashes();
    
    // Find the most recent entry with this hash
    const matchingEntries = storedHashes
      .filter(entry => entry.hash === hash)
      .sort((a, b) => b.createdAt - a.createdAt); // Sort by newest first
    
    return matchingEntries.length > 0 ? matchingEntries[0] : null;
  } catch (error) {
    console.error('Error finding analysis by hash:', error);
    return null;
  }
}

/**
 * Clean up orphaned hashes (hashes pointing to non-existent analyses)
 * This should be called periodically or when analyses are deleted
 */
export async function cleanupOrphanedHashes(validAnalysisIds: string[]): Promise<void> {
  try {
    const storedHashes = await getStoredHashes();
    const validHashes = storedHashes.filter(hashEntry => 
      validAnalysisIds.includes(hashEntry.analysisId)
    );
    
    if (validHashes.length !== storedHashes.length) {
      await Preferences.set({
        key: HASH_STORAGE_KEY,
        value: JSON.stringify(validHashes)
      });
      console.log(`Cleaned up ${storedHashes.length - validHashes.length} orphaned hashes`);
    }
  } catch (error) {
    console.error('Error cleaning up orphaned hashes:', error);
  }
}

/**
 * Remove hashes associated with a specific analysis ID
 */
export async function removeHashesByAnalysisId(analysisId: string): Promise<void> {
  try {
    const storedHashes = await getStoredHashes();
    const filteredHashes = storedHashes.filter(
      hashEntry => hashEntry.analysisId !== analysisId
    );
    
    await Preferences.set({
      key: HASH_STORAGE_KEY,
      value: JSON.stringify(filteredHashes)
    });
  } catch (error) {
    console.error('Error removing hashes by analysis ID:', error);
  }
}

/**
 * Get all hashes for a specific analysis ID
 */
export async function getHashesByAnalysisId(analysisId: string): Promise<ChatHashEntry[]> {
  try {
    const storedHashes = await getStoredHashes();
    return storedHashes.filter(hashEntry => hashEntry.analysisId === analysisId);
  } catch (error) {
    console.error('Error getting hashes by analysis ID:', error);
    return [];
  }
}