import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { handlePurchasePremium, handleRestorePurchases } from '@/services/purchases';
import { X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getLocalizedPricing } from '@/services/pricing';

interface RizzStyleSubscriptionModalProps {
  onClose: () => void;
  onCancel?: () => void;
  onSuccess: () => void;
}

const RizzStyleSubscriptionModal: React.FC<RizzStyleSubscriptionModalProps> = ({ onClose, onCancel, onSuccess }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragY, setDragY] = useState(0);
  const [localizedPrice, setLocalizedPrice] = useState('$4.99');
  const sheetRef = useRef<HTMLDivElement>(null);
  const startYRef = useRef(0);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchPrice = async () => {
      const pricing = await getLocalizedPricing();
      setLocalizedPrice(pricing.price);
    };
    fetchPrice();
  }, []);

  const handlePurchase = async () => {
    setIsLoading(true);
    try {
      const success = await handlePurchasePremium();
      if (success) {
        onSuccess(); // This handles both post-purchase action AND modal closing
      }
    } catch (error) {
      console.error('Purchase failed', error);
      // Error toast is likely handled in handlePurchasePremium
    } finally {
      setIsLoading(false);
    }
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    startYRef.current = e.touches[0].clientY;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    
    const currentY = e.touches[0].clientY;
    const deltaY = currentY - startYRef.current;
    
    // Only allow downward swipes
    if (deltaY > 0) {
      setDragY(deltaY);
    }
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    // If dragged down more than 100px, cancel the subscription flow
    if (dragY > 100) {
      onCancel ? onCancel() : onClose();
    } else {
      // Reset position
      setDragY(0);
    }
  };

  const handleEmailPress = () => {
    window.open('mailto:<EMAIL>', '_blank');
  };

  const handleTermsPress = () => {
    onCancel ? onCancel() : onClose();
    navigate('/terms-of-service');
  };

  const handlePrivacyPress = () => {
    onCancel ? onCancel() : onClose();
    navigate('/privacy-policy');
  };

  const handleRestorePress = async () => {
    setIsLoading(true);
    try {
      const success = await handleRestorePurchases();
      if (success) {
        onSuccess(); // This will trigger postPurchaseAction and close modal
      }
    } catch (error) {
      console.error('Restore failed', error);
      // Error toast is handled in handleRestorePurchases
    } finally {
      setIsLoading(false);
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
      {/* Bottom Sheet */}
      <div
        ref={sheetRef}
        className="w-full bg-gray-900 rounded-t-3xl animate-slide-up transition-transform duration-200"
        style={{
          transform: `translateY(${dragY}px)`,
          opacity: isDragging ? Math.max(0.5, 1 - dragY / 300) : 1
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Handle bar - touchable area for swiping */}
        <div
          className="flex justify-center pt-3 pb-2 cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            // Don't close on handle click, only on swipe
          }}
        >
          <div className="w-10 h-1 bg-gray-600 rounded-full"></div>
        </div>
        
        {/* Close button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
          className="absolute top-4 right-4 text-gray-500 hover:text-white z-10 p-2"
          type="button"
        >
          <X size={24} />
        </button>

        {/* Content */}
        <div className="px-8 pb-10 pt-4">
          <div className="flex flex-col items-center text-center">
            {/* Badge */}
            <div className="bg-blue-600/20 text-blue-200 text-xs px-3 py-1 rounded-full mb-6">
              Chatbuster Premium
            </div>

            {/* Main content */}
            <h2 className="text-2xl font-medium text-white mb-3">
              Discover Your Chat Secrets
            </h2>
            <p className="text-gray-400 text-sm mb-8">
              Start your <span className="text-blue-300 font-medium">risk-free trial</span>, then {localizedPrice}/week
            </p>

            {/* CTA Button */}
            <Button
              onClick={handlePurchase}
              disabled={isLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 rounded-lg transition-colors duration-200 text-base mb-6"
              type="button"
            >
              {isLoading ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Processing...
                </div>
              ) : (
                'Unlock Free Trial'
              )}
            </Button>

            {/* Footer links */}
            <div className="text-gray-500 text-xs flex items-center gap-4">
              <button
                onClick={handleEmailPress}
                className="hover:text-gray-300 transition-colors"
              >
                Mail
              </button>
              <button
                onClick={handleTermsPress}
                className="hover:text-gray-300 transition-colors"
              >
                Terms
              </button>
              <button
                onClick={handlePrivacyPress}
                className="hover:text-gray-300 transition-colors"
              >
                Privacy
              </button>
              <button
                onClick={handleRestorePress}
                disabled={isLoading}
                className="hover:text-gray-300 transition-colors disabled:opacity-50"
              >
                {isLoading ? 'Restoring...' : 'Restore'}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Backdrop - clicking outside cancels the subscription flow */}
      <div
        className="absolute inset-0 -z-10"
        onClick={onCancel ? onCancel : onClose}
      />
    </div>
  );
};

export default RizzStyleSubscriptionModal;