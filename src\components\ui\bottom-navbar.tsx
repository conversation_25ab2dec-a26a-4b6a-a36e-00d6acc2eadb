import React from 'react';
import { cn } from '@/lib/utils';
import { Zap, Users, HeartHandshake } from 'lucide-react';
import { ZapSolid, UsersSolid, HeartHandshakeSolid } from '@/components/ui/solid-icons';
import { hapticFeedback } from '@/utils/haptics';

interface BottomNavbarProps {
  activeView: string;
  onViewChange: (view: string) => void;
  className?: string;
}

const BottomNavbar: React.FC<BottomNavbarProps> = ({
  activeView,
  onViewChange,
  className
}) => {
  const navItems = [
    {
      id: 'group-summary',
      label: 'Squad Vibes',
      icon: Zap,
      solidIcon: ZapSolid
    },
    {
      id: 'connections',
      label: 'Connections',
      icon: HeartHandshake,
      solidIcon: HeartHandshakeSolid
    },
    {
      id: 'people-index',
      label: 'Compare',
      icon: Users,
      solidIcon: UsersSolid
    }
  ];

  return (
    <div className={cn(
      "fixed bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-t border-gray-300 shadow-xl navbar-with-safe-area",
      className
    )}
    style={{
      paddingBottom: 'env(safe-area-inset-bottom)'
    }}>
      <div className="grid grid-cols-3 px-4 py-2">
        {navItems.map((item) => {
          const isActive = activeView === item.id;
          const Icon = isActive ? item.solidIcon : item.icon;

          return (
            <button
              key={item.id}
              onClick={() => {
                hapticFeedback.navigationTap();
                
                // Scroll to top immediately when tab is clicked
                window.scrollTo(0, 0);
                document.documentElement.scrollTop = 0;
                document.body.scrollTop = 0;

                // Change the view
                onViewChange(item.id);

                // Additional scroll after a short delay to ensure content is rendered
                setTimeout(() => {
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }, 100);
              }}
              className={cn(
                "flex flex-col items-center justify-center py-1 px-3 transition-colors duration-150",
                isActive
                  ? "bg-pink-100 rounded-lg text-pink-700"
                  : "text-gray-600 hover:text-gray-700"
              )}
              aria-label={item.label}
              aria-current={isActive ? "page" : undefined}
            >
              <div className="flex items-center justify-center mb-1">
                <Icon className={cn("h-6 w-6", isActive ? "text-pink-600" : "text-gray-600")} />
              </div>
              <span className={cn(
                "text-xs font-medium",
                isActive ? "text-pink-700" : "text-gray-600"
              )}>
                {item.label}
              </span>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default BottomNavbar;
