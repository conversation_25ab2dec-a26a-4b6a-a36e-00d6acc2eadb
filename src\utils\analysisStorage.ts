import { Preferences } from '@capacitor/preferences';
import { removeHashesByAnalysisId } from './chatHashUtils';
import { VersionInfo, MigrationResult } from '@/types/versioning';
import { versionService } from '@/services/versionService';

/**
 * Utility functions for storing and retrieving chat analysis results with versioning support
 */

// Enhanced interface for analysis metadata with versioning
export interface AnalysisEntry {
  id: string;
  title: string;
  date: string;
  time: string;
  messageCount?: number;
  participants?: number;
  timestamp: number; // Unix timestamp for sorting
  analysisJson: any;
  analysisType: 'group' | 'love';
  selectedChatType: 'group' | 'one-on-one';
  originalFileName?: string;
  chatHash?: string; // SHA-256 hash of first 10 lines for duplicate detection
  
  // New versioning fields
  dataVersion?: string;        // API response format version
  appVersion?: string;         // App version that created this
  createdWith?: 'mobile' | 'web'; // Platform information
  migratedFrom?: string;       // Previous version if migrated
  migrationDate?: string;      // When migration occurred
}

// Legacy analysis entry (for backward compatibility)
export interface LegacyAnalysisEntry {
  id: string;
  title: string;
  date: string;
  time: string;
  messageCount?: number;
  participants?: number;
  timestamp: number;
  analysisJson: any;
  analysisType: 'group' | 'love';
  selectedChatType: 'group' | 'one-on-one';
  originalFileName?: string;
  chatHash?: string;
}

// Mock data for initial development
const mockAnalyses: AnalysisEntry[] = [
  {
    id: '1',
    title: 'Summer Trip Planning (Mock)',
    date: 'Today',
    time: '2:30 PM',
    messageCount: 1423,
    participants: 5,
    timestamp: Date.now() - 3600000, // 1 hour ago
    analysisJson: { mockData: true, title: 'Summer Trip Planning' },
    analysisType: 'group',
    selectedChatType: 'group',
    originalFileName: 'summer_trip.txt',
  },
  {
    id: '2',
    title: 'Gaming Squad (Mock)',
    date: 'Yesterday',
    time: '7:15 PM',
    messageCount: 856,
    participants: 4,
    timestamp: Date.now() - 86400000, // 1 day ago
    analysisJson: { mockData: true, title: 'Gaming Squad' },
    analysisType: 'group',
    selectedChatType: 'group',
    originalFileName: 'gaming_squad_chat.zip',
  },
  {
    id: '3',
    title: 'Family Group (Mock)',
    date: 'May 15, 2023',
    time: '10:45 AM',
    messageCount: 2134,
    participants: 6,
    timestamp: Date.now() - 7 * 86400000, // 7 days ago
    analysisJson: { mockData: true, title: 'Family Group' },
    analysisType: 'group',
    selectedChatType: 'group',
  },
  {
    id: '4',
    title: 'Work Team (Mock)',
    date: 'May 10, 2023',
    time: '3:20 PM',
    messageCount: 567,
    participants: 8,
    timestamp: Date.now() - 14 * 86400000, // 14 days ago
    analysisJson: { mockData: true, title: 'Work Team' },
    analysisType: 'love',
    selectedChatType: 'one-on-one',
    originalFileName: 'work_team_convo.txt',
  }
];

// Storage key for Capacitor Storage
const STORAGE_KEY = 'chat-vibe-analyzer-history';

/**
 * Get all saved analyses from Capacitor Storage with automatic migration
 */
export async function getSavedAnalyses(): Promise<AnalysisEntry[]> {
  try {
    const ret = await Preferences.get({ key: STORAGE_KEY });
    if (ret.value) {
      const rawAnalyses = JSON.parse(ret.value);
      
      // Process each analysis for potential migration
      const processedAnalyses: AnalysisEntry[] = [];
      let needsUpdate = false;
      
      for (const analysis of rawAnalyses) {
        const processed = await migrateAnalysisIfNeeded(analysis);
        processedAnalyses.push(processed.analysis);
        
        if (processed.migrated) {
          needsUpdate = true;
        }
      }
      
      // Save back to storage if any migrations occurred
      if (needsUpdate) {
        await Preferences.set({
          key: STORAGE_KEY,
          value: JSON.stringify(processedAnalyses),
        });
        console.log('Updated storage with migrated analyses');
      }
      
      return processedAnalyses;
    }
  } catch (error) {
    console.error('Error retrieving saved analyses:', error);
  }
  
  // Return empty array for fresh installs - no mock data
  return [];
}

/**
 * Save a new analysis to Capacitor Storage with versioning
 */
export async function saveAnalysis(
  analysisData: Omit<AnalysisEntry, 'id' | 'timestamp' | 'date' | 'time' | 'dataVersion' | 'appVersion' | 'createdWith'> & { title: string }
): Promise<AnalysisEntry> {
  try {
    const savedAnalyses = await getSavedAnalyses();
    
    const currentTimestamp = Date.now();
    
    // Get version information
    const versionInfo = versionService.createVersionInfo();
    
    // Create new analysis entry with ID, timestamp, and version info
    const newAnalysis: AnalysisEntry = {
      ...analysisData,
      id: currentTimestamp.toString(),
      timestamp: currentTimestamp,
      date: new Date(currentTimestamp).toLocaleDateString(),
      time: new Date(currentTimestamp).toLocaleTimeString(),
      dataVersion: versionInfo.dataVersion,
      appVersion: versionInfo.appVersion,
      createdWith: versionInfo.createdWith,
    };
    
    // Add to beginning of array (most recent first)
    const updatedAnalyses = [newAnalysis, ...savedAnalyses];
    
    await Preferences.set({
      key: STORAGE_KEY,
      value: JSON.stringify(updatedAnalyses),
    });
    
    console.log(`Saved analysis with version info:`, {
      id: newAnalysis.id,
      dataVersion: newAnalysis.dataVersion,
      appVersion: newAnalysis.appVersion,
      createdWith: newAnalysis.createdWith
    });
    
    return newAnalysis;
  } catch (error) {
    console.error('Error saving analysis:', error);
    // Re-throw or handle as appropriate for your app's error strategy
    throw error;
  }
}

/**
 * Delete an analysis from Capacitor Storage
 */
export async function deleteAnalysis(id: string): Promise<boolean> {
  try {
    const savedAnalyses = await getSavedAnalyses();
    const updatedAnalyses = savedAnalyses.filter(analysis => analysis.id !== id);
    
    await Preferences.set({
      key: STORAGE_KEY,
      value: JSON.stringify(updatedAnalyses),
    });
    
    // Also remove associated hashes
    await removeHashesByAnalysisId(id);
    
    return true;
  } catch (error) {
    console.error('Error deleting analysis:', error);
    return false;
  }
}

/**
 * Get a specific analysis by ID from Capacitor Storage with automatic migration
 */
export async function getAnalysisById(id: string): Promise<AnalysisEntry | null> {
  try {
    const savedAnalyses = await getSavedAnalyses(); // This already handles migration
    return savedAnalyses.find(analysis => analysis.id === id) || null;
  } catch (error) {
    console.error('Error retrieving analysis by ID:', error);
    return null;
  }
}

/**
 * Migrate an analysis entry if needed (from legacy format to versioned format)
 */
async function migrateAnalysisIfNeeded(analysis: any): Promise<{ analysis: AnalysisEntry; migrated: boolean }> {
  // Check if analysis already has version information
  if (analysis.dataVersion && analysis.appVersion) {
    return { analysis: analysis as AnalysisEntry, migrated: false };
  }
  
  // This is a legacy analysis that needs migration
  console.log(`Migrating legacy analysis: ${analysis.id}`);
  
  const currentVersions = versionService.getCurrentVersions();
  
  const migratedAnalysis: AnalysisEntry = {
    ...analysis,
    dataVersion: '1.0.0', // Assume legacy data is version 1.0.0
    appVersion: 'legacy',
    createdWith: 'unknown' as const,
    migratedFrom: 'legacy',
    migrationDate: new Date().toISOString(),
  };
  
  return { analysis: migratedAnalysis, migrated: true };
}

/**
 * Check if an analysis needs migration
 */
export function needsMigration(analysis: any): boolean {
  return !analysis.dataVersion || !analysis.appVersion;
}

/**
 * Get migration statistics for all stored analyses
 */
export async function getMigrationStats(): Promise<{
  total: number;
  migrated: number;
  legacy: number;
  versions: Record<string, number>;
}> {
  try {
    const analyses = await getSavedAnalyses();
    
    const stats = {
      total: analyses.length,
      migrated: 0,
      legacy: 0,
      versions: {} as Record<string, number>
    };
    
    for (const analysis of analyses) {
      if (analysis.migratedFrom) {
        stats.migrated++;
      }
      
      if (!analysis.dataVersion) {
        stats.legacy++;
      } else {
        stats.versions[analysis.dataVersion] = (stats.versions[analysis.dataVersion] || 0) + 1;
      }
    }
    
    return stats;
  } catch (error) {
    console.error('Error getting migration stats:', error);
    return { total: 0, migrated: 0, legacy: 0, versions: {} };
  }
}


/**
 * Force migration of all analyses (useful for testing or manual migration)
 */
export async function forceFullMigration(): Promise<MigrationResult> {
  try {
    const ret = await Preferences.get({ key: STORAGE_KEY });
    if (!ret.value) {
      return { success: true, toVersion: '1.0.0', migratedFields: [] };
    }
    
    const rawAnalyses = JSON.parse(ret.value);
    const migratedAnalyses: AnalysisEntry[] = [];
    const migratedFields: string[] = [];
    
    for (const analysis of rawAnalyses) {
      const result = await migrateAnalysisIfNeeded(analysis);
      migratedAnalyses.push(result.analysis);
      
      if (result.migrated) {
        migratedFields.push(analysis.id);
      }
    }
    
    await Preferences.set({
      key: STORAGE_KEY,
      value: JSON.stringify(migratedAnalyses),
    });
    
    console.log(`Force migration completed. Migrated ${migratedFields.length} analyses.`);
    
    return {
      success: true,
      toVersion: '1.0.0',
      migratedFields,
    };
  } catch (error) {
    console.error('Error during force migration:', error);
    return {
      success: false,
      toVersion: '1.0.0',
      errors: [error instanceof Error ? error.message : 'Unknown error']
    };
  }
}
