import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { MessageSquare, Book } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SlangTerm {
  term: string;
  definition: string;
  count: number;
}

interface CommonPhrase {
  phrase: string;
  count: number;
  user: string;
  context?: string;
  examples?: string[];
  relatedPhrases?: string[];
  timeUsage?: {
    morning: number;
    afternoon: number;
    evening: number;
    night: number;
  };
  funFacts?: string[];
}

interface LingoLabNoPhrasesProps {
  slangCloud: SlangTerm[];
  commonPhrases: CommonPhrase[];
  dashboardGroupSlang: SlangTerm[];
}

const LingoLabNoPhrase: React.FC<LingoLabNoPhrasesProps> = ({
  slangCloud = [],
  commonPhrases = []
}) => {
  const [selectedTerm, setSelectedTerm] = useState<SlangTerm | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  // Handle click on a term
  const handleTermClick = (term: SlangTerm) => {
    setSelectedTerm(term);
    setDialogOpen(true);
  };

  // Get color based on index
  const getColor = (index: number) => {
    const colors = [
      'text-astro-mint',
      'text-astro-blue',
      'text-astro-coral',
      'text-astro-yellow',
      'text-astro-lavender',
      'text-primary'
    ];
    return colors[index % colors.length];
  };

  // Get font size based on count
  const getFontSize = (count: number) => {
    const maxCount = Math.max(...slangCloud.map(t => t.count));
    const minCount = Math.min(...slangCloud.map(t => t.count));
    const range = maxCount - minCount;
    
    // Calculate size between 0.8rem and 1.5rem based on count
    const minSize = 0.8;
    const maxSize = 1.5;
    const size = minSize + ((count - minCount) / (range || 1)) * (maxSize - minSize);
    
    return `${size}rem`;
  };

  return (
    <div className="space-y-4">
      {/* Card 1: Top Slang/Word Cloud */}
      <Card className="overflow-hidden border shadow-sm">
        <CardContent className="p-0">
          <div className="p-4 bg-gradient-to-r from-astro-mint/20 to-astro-blue/20">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-gradient-to-r from-astro-mint to-astro-blue flex items-center justify-center text-white">
                  <MessageSquare className="h-4 w-4" />
                </div>
                <h3 className="font-semibold">Words You Bust Out Most!</h3>
              </div>
            </div>

            <p className="text-xs text-gray-600 mb-3">
              Your squad has its own language! Here are the words that define your chat vibe.
            </p>
          </div>

          {/* Word Cloud */}
          <div className="p-4">
            <div className="relative h-60 w-full">
              {/* Scrollable container for word bubbles */}
              <div className="h-full w-full overflow-y-auto overflow-x-hidden custom-scrollbar">
                <div className="flex flex-wrap justify-center items-start gap-2 p-2 min-h-full">
                  {slangCloud.map((term, index) => (
                    <div
                      key={index}
                      className={cn(
                        "cursor-pointer px-3 py-1.5 rounded-full transition-all hover:scale-105 break-words max-w-full",
                        getColor(index),
                        term.count > (Math.max(...slangCloud.map(t => t.count)) * 0.7) ? "font-bold" :
                        term.count > (Math.max(...slangCloud.map(t => t.count)) * 0.4) ? "font-medium" : "font-normal"
                      )}
                      style={{
                        fontSize: getFontSize(term.count),
                        backgroundColor: `${getColor(index).replace('text', 'bg')}/10`,
                        backdropFilter: 'blur(2px)',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        boxShadow: term.count > (Math.max(...slangCloud.map(t => t.count)) * 0.7)
                          ? '0 3px 6px rgba(0, 0, 0, 0.1)'
                          : '0 2px 4px rgba(0, 0, 0, 0.05)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                        margin: term.count > (Math.max(...slangCloud.map(t => t.count)) * 0.7) ? '4px' :
                               term.count > (Math.max(...slangCloud.map(t => t.count)) * 0.4) ? '3px' : '2px',
                        display: 'inline-block',
                        maxWidth: 'calc(100% - 16px)', // Ensure it doesn't overflow container width
                      }}
                      onClick={() => handleTermClick(term)}
                    >
                      {term.term}
                    </div>
                  ))}
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute inset-0 flex items-center justify-center opacity-5 pointer-events-none -z-10">
                <div className="w-40 h-40 rounded-full border-4 border-dashed border-astro-mint"></div>
              </div>

              {/* Gradient overlay to indicate scrollable content */}
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>
            </div>

            <div className="text-xs text-center text-gray-500 mt-2">
              Click on any word to see its definition
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dialog for showing term details */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Book className="h-5 w-5" />
              <span>{selectedTerm?.term}</span>
            </DialogTitle>
          </DialogHeader>

          {selectedTerm && (
            <div className="space-y-4">
              <div className="bg-gradient-to-r from-astro-mint/10 to-astro-blue/10 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-sm font-medium">Usage Count</h4>
                  <span className="text-lg font-bold">{selectedTerm.count}x</span>
                </div>
                <p className="text-sm text-gray-600">
                  {selectedTerm.definition || `This term appears frequently in your group chat.`}
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LingoLabNoPhrase;
