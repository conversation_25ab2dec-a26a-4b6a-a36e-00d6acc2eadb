import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';

import { Info, HelpCircle, History, Download, BarChart3 } from 'lucide-react';
import { WhatsAppIcon, TelegramIcon, DiscordIcon, InstagramIcon } from '@/components/icons/PlatformIcons';
import JSZip from 'jszip';
import { analyzeChatFile } from '@/services/api';
import { useAnalysis } from '@/contexts/AnalysisContext';
import { AnalysisState, useAppState } from '@/contexts/AppStateContext';
import { isGroupChat } from '@/utils/chatTypeDetector';
import { demoData } from '@/data/analysisData';
import { isProductionMode, isDevelopmentMode } from '@/utils/environment';
import { toast } from '@/utils/toast';
import { hapticFeedback } from '@/utils/haptics';
import ChatTypeSelector, { ChatType } from './ChatTypeSelector';
import { motion, AnimatePresence } from 'framer-motion';
import { Haptics, ImpactStyle } from '@capacitor/haptics';
import { getSavedAnalyses } from '../utils/analysisStorage'; // Add this import at the top

// Add custom styles for the slide-up animation
const slideUpStyles = `
  @keyframes slide-up {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  @keyframes breathe {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-2px);
    }
  }

  .menu-slide-up {
    animation: slide-up 0.3s ease-out;
  }

  .menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
    opacity: 0;
    transition: opacity 0.3s ease-out;
  }

  .menu-overlay.active {
    opacity: 1;
  }
`;

interface UploadSectionProps {
  // onAnalyze prop is defined but not used in this component
  // It's kept for compatibility with other components that might use it
  onAnalyze?: (text: string, title?: string, platform?: string) => void;
}

const UploadSection = ({}: UploadSectionProps) => {
  const navigate = useNavigate();
  const { 
    setState, 
    showSubscriptionModal, 
    showDailyLimitDialog 
  } = useAppState();
  const { setAnalysisData, setIsLoadingFromHistory } = useAnalysis();
  const [isDragging, setIsDragging] = useState(false);
  const [pastedText, setPastedText] = useState('');
  const [isFileProcessedByHandler, setIsFileProcessedByHandler] = useState(false);
  const [hasHistory, setHasHistory] = useState<boolean>(false);

  // Check for history on component mount
  useEffect(() => {
    const checkHistory = async () => {
      try {
        const savedAnalyses = await getSavedAnalyses();
        setHasHistory(savedAnalyses.length > 0);
      } catch (error) {
        console.error('Error checking history:', error);
        setHasHistory(false);
      }
    };
    
    checkHistory();
  }, []);

  // Listen for file processing from FileHandler
  useEffect(() => {
    const handleFileProcessed = (event: CustomEvent) => {
      const { text, fileName } = event.detail;
      console.log('UploadSection: Received file from FileHandler:', fileName);
      setPastedText(text);
      setIsFileProcessedByHandler(true); // Mark that file was processed by FileHandler
    };

    // Listen for file processing events from FileHandler
    window.addEventListener('fileProcessed', handleFileProcessed as EventListener);

    return () => {
      window.removeEventListener('fileProcessed', handleFileProcessed as EventListener);
    };
  }, []);

  // Reset the flag when pastedText becomes empty (user clears it)
  useEffect(() => {
    if (!pastedText || pastedText.trim().length === 0) {
      setIsFileProcessedByHandler(false);
    }
  }, [pastedText]);
  
  // Helper function to detect if error is specifically a daily limit error
  const isDailyLimitError = (error: any): boolean => {
    const errorMessage = error.message?.toLowerCase() || '';
    console.log('🔍 [UploadSection] Checking if daily limit error:', errorMessage);
    console.log('🔍 [UploadSection] Full error object:', error);
    
    const isDailyLimit = errorMessage.includes('daily credit limit') || 
                        errorMessage.includes('daily limit') ||
                        errorMessage.includes('daily credit limit reached') ||
                        errorMessage.includes('analysis quota exceeded') ||
                        errorMessage.includes('quota exceeded');
    
    console.log('🎯 [UploadSection] Daily limit detection result:', isDailyLimit);
    return isDailyLimit;
  };
  
  // Track selected platform for showing appropriate instructions
  const [selectedPlatform, setSelectedPlatform] = useState('whatsapp');
  const [isProcessing, setIsProcessing] = useState(false);
  const [chatType, setChatType] = useState<ChatType>('group');
  const prevChatTypeRef = useRef<ChatType>('group');
  
  // Demo mode toggle - set to true to always show demo button
  const FORCE_SHOW_DEMO = true; // Change this to false to use normal environment detection
  const shouldShowDemo = FORCE_SHOW_DEMO || isDevelopmentMode();
  
  // Screen height state for dynamic margins
  const [screenHeight, setScreenHeight] = useState(window.innerHeight);

  // Update screen height on resize
  useEffect(() => {
    const handleResize = () => setScreenHeight(window.innerHeight);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Dynamic margin calculation based on screen height
  const getDynamicMargin = () => {
    if (screenHeight <= 700) return 'mb-1';
    if (screenHeight <= 800) return 'mb-1';
    if (screenHeight <= 900) return 'mb-2';
    if (screenHeight <= 1000) return 'mb-3';
    return 'mb-3';
  };

  // Dynamic margin for main image with larger spacing
  const getImageDynamicMargin = () => {
    if (screenHeight <= 700) return 'mt-6 mb-6';
    if (screenHeight <= 800) return 'mt-8 mb-8';
    if (screenHeight <= 900) return 'mt-10 mb-10';
    if (screenHeight <= 1000) return 'mt-12 mb-12';
    return 'mt-14 mb-14';
  };

  const getDynamicMarginLarge = () => {
    if (screenHeight <= 700) return 'mb-1';
    if (screenHeight <= 800) return 'mb-1';
    if (screenHeight <= 900) return 'mb-2';
    if (screenHeight <= 1000) return 'mb-3';
    return 'mb-3';
  };

  // Dynamic sizing for ChatTypeSelector based on screen height
  const getChatTypeSelectorSize = (): 'xs' | 'sm' | 'md' | 'lg' | 'xl' => {
    if (screenHeight <= 700) return 'xs';
    if (screenHeight <= 800) return 'sm';
    if (screenHeight <= 900) return 'md';
    if (screenHeight <= 1000) return 'lg';
    return 'xl';
  };

  // Dynamic sizing for Platform buttons based on screen height
  const getPlatformButtonContainerClasses = () => {
    if (screenHeight <= 700) return `grid grid-cols-2 gap-1.5 max-w-[320px] ${getDynamicMargin()} mx-auto justify-center items-center`;
    if (screenHeight <= 800) return `grid grid-cols-2 gap-2 max-w-[340px] ${getDynamicMargin()} mx-auto justify-center items-center`;
    if (screenHeight <= 900) return `grid grid-cols-2 sm:grid-cols-4 gap-3 max-w-[360px] sm:max-w-lg ${getDynamicMargin()} mx-auto justify-center items-center`;
    if (screenHeight <= 1000) return `grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 max-w-[380px] sm:max-w-xl ${getDynamicMarginLarge()} mx-auto justify-center items-center`;
    return `grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 max-w-[400px] sm:max-w-2xl ${getDynamicMarginLarge()} mx-auto justify-center items-center`;
  };

  const getPlatformButtonClasses = () => {
    if (screenHeight <= 700) return 'h-[55px] p-1 gap-1 rounded-xl';
    if (screenHeight <= 800) return 'h-[75px] p-2.5 gap-1 rounded-xl';
    if (screenHeight <= 900) return 'h-[80px] p-2.5 gap-1 rounded-2xl';
    if (screenHeight <= 1000) return 'h-[85px] p-2.5 gap-1 rounded-2xl';
    return 'h-[75px] p-3 gap-1.5 rounded-2xl';
  };

  const getPlatformIconClasses = (isSelected: boolean) => {
    if (screenHeight <= 700) {
      return isSelected ? 'h-3.5 w-3.5' : 'h-3 w-3 group-hover:scale-110';
    }
    if (screenHeight <= 800) {
      return isSelected ? 'h-5 w-5' : 'h-4 w-4 group-hover:scale-110';
    }
    if (screenHeight <= 900) {
      return isSelected ? 'h-4.5 w-4.5' : 'h-4 w-4 group-hover:scale-110';
    }
    if (screenHeight <= 1000) {
      return isSelected ? 'h-5.5 w-5.5' : 'h-5 w-5 group-hover:scale-110';
    }
    return isSelected ? 'h-6 w-6' : 'h-5 w-5 group-hover:scale-110';
  };

  const getPlatformTextClasses = () => {
    if (screenHeight <= 700) return 'text-[10px] font-medium leading-tight';
    if (screenHeight <= 800) return 'text-[14px] font-semibold leading-tight';
    if (screenHeight <= 900) return 'text-[16px] font-semibold leading-tight';
    return 'text-[18px] font-semibold leading-tight';
  };

  const getPlatformBadgeClasses = () => {
    if (screenHeight <= 700) return 'text-[6px] px-0.5 py-0.5 -top-0.5 -right-0.5';
    if (screenHeight <= 800) return 'text-[10px] px-1 py-0.5 -top-1 -right-1';
    return 'text-[9px] px-1.5 py-0.5 -top-1 -right-1';
  };

  // Dynamic font sizing for text content based on screen height
  const getContentTextClasses = () => {
    if (screenHeight <= 700) return 'text-[10px]';
    if (screenHeight <= 800) return 'text-sm';
    if (screenHeight <= 900) return 'text-xl';
    if (screenHeight <= 1000) return 'text-base';
    return 'text-base';
  };

  const getContentTitleClasses = () => {
    if (screenHeight <= 700) return 'text-[15px]';
    if (screenHeight <= 800) return 'text-[16px]';
    if (screenHeight <= 900) return 'text-[18px]';
    if (screenHeight <= 1000) return 'text-[22px]';
    return 'text-[24px]';
  };

  const getContentSmallTextClasses = () => {
    if (screenHeight <= 700) return 'text-[10px]';
    if (screenHeight <= 800) return 'text-[12px]';
    if (screenHeight <= 900) return 'text-[13px]';
    if (screenHeight <= 1000) return 'text-[16px]';
    return 'text-[17px]';
  };

  const getContentStepTextClasses = () => {
    if (screenHeight <= 700) return 'text-[10px]';
    if (screenHeight <= 800) return 'text-[12px]';
    if (screenHeight <= 900) return 'text-[13px]';
    if (screenHeight <= 1000) return 'text-[16px]';
    return 'text-[17px]';
  };

  // Dynamic step circle sizes
  const getStepCircleClasses = () => {
    if (screenHeight <= 700) return 'w-5 h-5';
    if (screenHeight <= 800) return 'w-5 h-5';
    return 'w-6 h-6';
  };

  const getStepMarginClasses = () => {
    if (screenHeight <= 700) return 'mr-1.5 mt-0';
    if (screenHeight <= 800) return 'mr-1.5 mt-0.5';
    return 'mr-2 mt-0.5';
  };

  // Prevent 1-on-1 selection in production mode
  const handleChatTypeChange = (type: ChatType) => {
    if (isProductionMode() && type === 'one-on-one') {
      // Removed toast - user can see this is coming soon from UI
      return;
    }
    setChatType(type);
  };
  const [emojiParticles, setEmojiParticles] = useState<Array<{
    id: number;
    emoji: string;
    startX: number;
    startY: number;
    centerX: number;
    centerY: number;
    delay: number;
  }>>([]);

  // Emoji sets for different chat types
  const loveEmojis = ['💕', '💖', '💗', '💝', '💘', '💞', '💓', '💯', '🥰', '😍', '🤗', '💋', '🌹', '✨'];
  const funEmojis = ['🎉', '🎊', '😂', '🤣', '😆', '🎈', '🥳', '🎁', '🍾', '🌟', '⭐', '🎯', '🚀', '💥'];

  // Trigger emoji burst animation
  const triggerEmojiBurst = (chatType: ChatType) => {
    const emojis = chatType === 'one-on-one' ? loveEmojis : funEmojis;
    const particleCount = 12 + Math.floor(Math.random() * 8); // Random between 12-19 particles
    
    // Get viewport dimensions for precise positioning
    const viewportWidth = Math.max(
      document.documentElement.clientWidth || 0,
      window.innerWidth || 0
    );
    const viewportHeight = Math.max(
      document.documentElement.clientHeight || 0,
      window.innerHeight || 0
    );
    const centerX = viewportWidth / 2.5;
    const centerY = viewportHeight / 6;
    
    const newParticles = Array.from({ length: particleCount }, (_, i) => ({
      id: Date.now() + i,
      emoji: emojis[Math.floor(Math.random() * emojis.length)],
      startX: Math.random() < 0.5 ? -60 : viewportWidth+60 , // Start beyond screen edges
      startY: viewportHeight * 0.3 + Math.random() * (viewportHeight * 0.4), // Random Y in middle area
      centerX: centerX, // Store exact center for animation
      centerY: centerY, // Store exact center Y for animation
      delay: Math.random() * 0.5, // Stagger the animations with more variation
    }));

    setEmojiParticles(newParticles);

    // Clear particles after animation completes
    setTimeout(() => {
      setEmojiParticles([]);
    }, 2300); // Adjusted timeout to match shorter animation duration
  };

  // Determine animation direction based on chat type transition
  const getAnimationDirection = () => {
    const prev = prevChatTypeRef.current;
    const current = chatType;
    
    console.log('Animation Debug:', { prev, current, chatType });
    
    // Trigger emoji burst on chat type change (but not on initial load)
    if (prev !== current && prev) {
      triggerEmojiBurst(current);
    }
    
    // Update ref for next time
    prevChatTypeRef.current = current;
    
    // Group -> 1-on-1: slide in from left (1-on-1 is on the left side)
    // 1-on-1 -> Group: slide in from right (group is on the right side)
    if (prev === 'group') {
      
      return { initial: '100%', exit: '100%' };
    } else if (prev === 'one-on-one') {
      
      return { initial: '-100%', exit: '-100%' };
    }
    
    
    // Default direction (for initial load or processing state)
    return { initial: '-100%', exit: '-100%' };
  };

  const animationDirection = getAnimationDirection();

  // Highlight WhatsApp button by default when component mounts
  useEffect(() => {
    // Add a small delay to ensure DOM is fully loaded
    setTimeout(() => {
      const whatsappButton = document.querySelector('.platform-btn');
      if (whatsappButton) {
        whatsappButton.classList.add('bg-gray-50');
      }
    }, 100);
  }, []);

  // Store the selected analysis type in local storage for persistence across components
  useEffect(() => {
    const apiAnalysisType = chatType === 'one-on-one' ? 'love' : 'group';
    localStorage.setItem('selectedAnalysisType', apiAnalysisType);
    console.log(`UploadSection: Stored analysis type in localStorage: ${apiAnalysisType}`);
    
    // Trigger haptic feedback when storing analysis type
    hapticFeedback.chatTypeSwitch();
  }, [chatType]);

  const handlePaste = (e: React.ClipboardEvent) => {
    const text = e.clipboardData.getData('text');
    if (text) {
      setPastedText(text);
      setIsFileProcessedByHandler(false); // Reset flag when user pastes text manually
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];

      // Check if it's a supported file type
      if (file.name.toLowerCase().endsWith('.zip') || file.name.toLowerCase().endsWith('.txt')) {
        try {
          // Show loading state
          setPastedText(`Processing ${file.name.toLowerCase().endsWith('.zip') ? 'zip' : 'text'} file...`);
          setIsProcessing(true);

          // For display purposes, try to extract text content
          let displayText = '';

          if (file.name.toLowerCase().endsWith('.zip')) {
            try {
              // Process zip file on the client side to show a preview
              const zipData = await file.arrayBuffer();
              const zip = await JSZip.loadAsync(zipData);

              // Find the WhatsApp chat export file
              let chatFile = null;

              // First, try to find a file with "_chat.txt" in the name
              for (const [path, zipEntry] of Object.entries(zip.files)) {
                if (path.toLowerCase().includes('_chat.txt') && !zipEntry.dir) {
                  chatFile = zipEntry;
                  break;
                }
              }

              // If no "_chat.txt" file found, look for any .txt file
              if (!chatFile) {
                for (const [path, zipEntry] of Object.entries(zip.files)) {
                  if (path.toLowerCase().endsWith('.txt') && !zipEntry.dir) {
                    chatFile = zipEntry;
                    break;
                  }
                }
              }

              if (chatFile) {
                // Extract the text content for preview
                displayText = await chatFile.async('string');
                setPastedText(displayText);
              } else {
                setPastedText('No chat text file found in the zip archive. Sending to server for processing...');
              }
            } catch (zipError) {
              console.error('Error extracting preview from zip:', zipError);
              setPastedText('Processing zip file...');
            }
          } else {
            // For text files, read content for preview
            try {
              displayText = await file.text();
              setPastedText(displayText);
            } catch (textError) {
              console.error('Error reading text file:', textError);
              setPastedText('Processing text file...');
            }
          }

          // Send the file to the backend for analysis
          setState(AnalysisState.ANALYZING);

          // Send the file directly to the backend with the selected analysis type
          const apiAnalysisType = chatType === 'one-on-one' ? 'love' : 'group';
          console.log(`UploadSection: Sending file with analysis type: ${apiAnalysisType}`);
          console.log(`UploadSection: Chat type is ${chatType}, which maps to API analysis type ${apiAnalysisType}`);
          const result = await analyzeChatFile(file, apiAnalysisType);

          // Store the analysis result in the context
          // Also store the selected chat type for use in navigation
          setAnalysisData({
            source: result.source as 'api' | 'file' | 'mock',
            fileName: result.fileName,
            text: result.text || displayText,
            result: result.result,
            selectedChatType: chatType, // Store the user's selection
            analysisType: apiAnalysisType // Store the API analysis type
          });

          // Use the user's selected chat type for routing
          console.log(`UploadSection: User selected chat type: ${chatType}`);

          // Navigate based on the user's selected chat type
          if (chatType === 'one-on-one') {
            // For 1-on-1 chats, navigate to the relationship type questionnaire
            console.log('UploadSection: Navigating to relationship type questionnaire for 1-on-1 chat');
            navigate('/relationship-type', { replace: true });
          } else {
            // For group chats, go to the dashboard
            console.log('UploadSection: Setting state to DASHBOARD for group chat');
            setState(AnalysisState.DASHBOARD);
            // Navigate to the home page if not already there
            navigate('/', { replace: true });
          }

        } catch (error) {
          console.error('Error processing file:', error);
          
          // Handle subscription errors specifically
          if (error.name === 'SubscriptionRequiredError') {
            console.log('[UploadSection] SubscriptionRequiredError caught');
            
            // Check if it's specifically a daily limit error
            if (isDailyLimitError(error)) {
              console.log('[UploadSection] Daily limit error detected, showing daily limit dialog');
              showDailyLimitDialog();
            } else {
              console.log('[UploadSection] General subscription error, showing subscription modal');
              showSubscriptionModal();
            }
            return;
          }
          
          const errorMessage = error instanceof Error ? error.message : String(error);
          const userMessage = isProductionMode()
            ? 'Unable to process the file. Please try again with a valid WhatsApp export.'
            : `Error processing file: ${errorMessage}`;
          
          hapticFeedback.fileUpload();
          // Removed toast - error handling should be done via dialogs
          setPastedText('Error processing file. Please try again.');

          // In production, don't navigate to results on error - stay on upload screen
          if (isProductionMode()) {
            setState(AnalysisState.UPLOAD);
          } else {
            // Even in error cases in development, respect the user's selection
            console.log(`UploadSection (drop error): Using user selected chat type: ${chatType}`);

            if (chatType === 'one-on-one') {
              navigate('/relationship-type', { replace: true });
            } else {
              setState(AnalysisState.DASHBOARD);
            }
          }
        } finally {
          setIsProcessing(false);
        }
      } else {
        // Removed toast - user can see file type requirements in UI
      }
    }
  };

  const handleAnalyzeClick = async () => {
    // Show loading screen first, then handle production mode
    setState(AnalysisState.ANALYZING);

    // In production mode, redirect to tutorial instead of analyzing
    if (isProductionMode()) {
      console.log('Production mode - redirecting to tutorial');
      // Add a small delay to show the loading screen briefly
      setTimeout(() => {
        navigate('/tutorial', { replace: true });
      }, 500);
      return;
    }

    // File was already processed by FileHandler, continue with normal analysis flow
    if (isFileProcessedByHandler) {
      console.log('UploadSection: File already processed by FileHandler - continuing with analysis');
      // Let the API handle subscription validation
    }

    try {
      setIsProcessing(true);
      // setState(AnalysisState.ANALYZING) already called above

      // Default title would be 'WhatsApp Chat' if needed

      // Check if we have text to analyze
      if (!pastedText || pastedText.trim().length < 10) {
        if (isProductionMode()) {
          // In production, show error and stay on upload screen
          hapticFeedback.error();
          // Removed toast - user can see this requirement in UI
          setState(AnalysisState.UPLOAD);
          return;
        } else {
          // In development, use mock data
          console.warn('Text too short or empty, using mock data');
          hapticFeedback.analyzeStart();
          toast.dev('No text provided - using demo data for development');

          const apiAnalysisType = chatType === 'one-on-one' ? 'love' : 'group';
          setAnalysisData({
            source: 'mock',
            fileName: '_chat.txt',
            text: pastedText || '',
            result: demoData,
            selectedChatType: chatType, // Store the user's selection
            analysisType: apiAnalysisType // Store the API analysis type
          });

          // Use the user's selected chat type for mock data too
          console.log(`UploadSection (mock data): Using user selected chat type: ${chatType}`);

          if (chatType === 'one-on-one') {
            // For 1-on-1 chats, navigate to the relationship type questionnaire
            console.log('UploadSection (mock data): Navigating to relationship type questionnaire for 1-on-1 chat');
            navigate('/relationship-type', { replace: true });
          } else {
            // For group chats, go to the dashboard
            console.log('UploadSection (mock data): Setting state to DASHBOARD for group chat');
            setState(AnalysisState.DASHBOARD);
            // Navigate to the home page if not already there
            navigate('/', { replace: true });
          }
          return;
        }
      }

      // Create a file from the pasted text
      const fileName = '_chat.txt'; // Use WhatsApp format for better backend processing
      const file = new File([pastedText], fileName, { type: 'text/plain' });

      // Send the file to the backend for analysis with the selected analysis type
      const apiAnalysisType = chatType === 'one-on-one' ? 'love' : 'group';
      console.log(`UploadSection (analyze): Sending file with analysis type: ${apiAnalysisType}`);
      console.log(`UploadSection (analyze): Chat type is ${chatType}, which maps to API analysis type ${apiAnalysisType}`);
      const result = await analyzeChatFile(file, apiAnalysisType);

      // Store the analysis result in the context
      // Also store the selected chat type for use in navigation
      setAnalysisData({
        source: result.source as 'api' | 'file' | 'mock',
        fileName: result.fileName,
        text: result.text || pastedText,
        result: result.result,
        selectedChatType: chatType, // Store the user's selection
        analysisType: apiAnalysisType // Store the API analysis type
      });

      // Use the user's selected chat type for routing
      console.log(`UploadSection (analyze): User selected chat type: ${chatType}`);

      // Navigate based on the user's selected chat type
      if (chatType === 'one-on-one') {
        // For 1-on-1 chats, navigate to the relationship type questionnaire
        console.log('UploadSection (analyze): Navigating to relationship type questionnaire for 1-on-1 chat');
        navigate('/relationship-type', { replace: true });
      } else {
        // For group chats, go to the dashboard
        console.log('UploadSection (analyze): Setting state to DASHBOARD for group chat');
        setState(AnalysisState.DASHBOARD);
        // Navigate to the home page if not already there
        navigate('/', { replace: true });
      }

    } catch (error) {
      console.error('Error analyzing text:', error);
      
      // Handle subscription errors specifically
      if (error.name === 'SubscriptionRequiredError') {
        console.log('[UploadSection] SubscriptionRequiredError caught (text analysis)');
        
        // Check if it's specifically a daily limit error
        if (isDailyLimitError(error)) {
          console.log('[UploadSection] Daily limit error detected, showing daily limit dialog');
          showDailyLimitDialog();
        } else {
          console.log('[UploadSection] General subscription error, showing subscription modal');
          showSubscriptionModal();
        }
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      const userMessage = isProductionMode()
        ? 'Unable to analyze your chat right now. Please check your connection and try again.'
        : `Error analyzing text: ${errorMessage}`;
      
      hapticFeedback.analysisError();
      // Removed toast - error handling should be done via dialogs

      // In production, don't navigate to results on error - stay on upload screen
      if (isProductionMode()) {
        setState(AnalysisState.UPLOAD);
      } else {
        // Even in error cases in development, respect the user's selection
        console.log(`UploadSection (error): Using user selected chat type: ${chatType}`);

        if (chatType === 'one-on-one') {
          navigate('/relationship-type', { replace: true });
        } else {
          setState(AnalysisState.DASHBOARD);
        }
      }
    } finally {
      setIsProcessing(false);
    }
  };

  // Dynamic sizing functions for the new action buttons based on screen height - positioned lower with reduced bottom space
  const getActionButtonContainerClasses = () => {
    if (screenHeight <= 700) return `flex gap-2 max-w-[400px] mt-8 mb-6 mx-auto justify-center items-center`;
    if (screenHeight <= 800) return `flex gap-3 max-w-[450px] mt-10 mb-8 mx-auto justify-center items-center`;
    if (screenHeight <= 900) return `flex gap-3 max-w-lg mt-12 mb-10 mx-auto justify-center items-center`;
    if (screenHeight <= 1000) return `flex gap-4 max-w-xl mt-14 mb-12 mx-auto justify-center items-center`;
    return `flex gap-4 max-w-2xl mt-16 mb-14 mx-auto justify-center items-center`;
  };

  const getActionButtonClasses = () => {
    if (screenHeight <= 700) return 'h-[48px] px-4 py-2.5 gap-2 rounded-xl text-sm font-medium';
    if (screenHeight <= 800) return 'h-[52px] px-4 py-3 gap-2 rounded-xl text-base font-medium';
    if (screenHeight <= 900) return 'h-[56px] px-5 py-3 gap-2 rounded-xl text-base font-medium';
    if (screenHeight <= 1000) return 'h-[60px] px-5 py-3.5 gap-2 rounded-xl text-base font-medium';
    return 'h-[64px] px-6 py-4 gap-2 rounded-xl text-lg font-medium';
  };

  const getActionButtonIconClasses = () => {
    if (screenHeight <= 700) return 'h-5 w-5';
    if (screenHeight <= 800) return 'h-5 w-5';
    if (screenHeight <= 900) return 'h-5 w-5';
    if (screenHeight <= 1000) return 'h-6 w-6';
    return 'h-6 w-6';
  };

  // Dynamic upload container classes for consistent width alignment
  const getUploadContainerClasses = () => {
    if (screenHeight <= 700) return 'w-full max-w-[320px] mx-auto h-auto border-2 border-dashed rounded-lg p-2 cursor-pointer transition-all duration-300';
    if (screenHeight <= 800) return 'w-full max-w-[300px] mx-auto h-auto border-2 border-dashed rounded-xl p-2 cursor-pointer transition-all duration-300';
    return 'w-full h-auto border-2 border-dashed rounded-xl p-2 sm:p-3 cursor-pointer transition-all duration-300';
  };

  // Dynamic content padding for upload area
  const getContentPaddingClasses = () => {
    if (screenHeight <= 700) return 'p-2';
    if (screenHeight <= 800) return 'p-2';
    return 'p-3';
  };

  const getContentSpacingClasses = () => {
    if (screenHeight <= 700) return 'space-y-0.5';
    if (screenHeight <= 800) return 'space-y-1';
    return 'space-y-1.5';
  };

  const getContentMarginClasses = () => {
    if (screenHeight <= 700) return 'mb-1';
    if (screenHeight <= 800) return 'mb-1.5';
    return 'mb-2';
  };



  return (
    <>


      {/* Scrollable Content */}
      <div className="space-y-3 sm:space-y-4 animate-slide-up max-w-full sm:max-w-2xl md:max-w-4xl lg:max-w-6xl mx-auto px-2 sm:px-3 md:px-4 py-2 sm:py-4 pb-2 sm:pb-4">
      <style dangerouslySetInnerHTML={{ __html: slideUpStyles }} />
      <div className="text-center flex-shrink-0">
        {/* Chat Type Selector */}
        <div className={`${getDynamicMargin()} sm:${getDynamicMargin()}`}>
          <ChatTypeSelector
            selectedType={chatType}
            onChange={handleChatTypeChange}
            className="mx-auto"
            size={getChatTypeSelectorSize()}
          />
        </div>

        {/* Main Image */}
        <div className={getImageDynamicMargin()}>
          <img 
            src="/mainscreen.webp" 
            alt="ChatBuster Main Screen"
            className="mx-auto max-w-full h-auto"
            style={{ maxHeight: '40vh' }}
          />
        </div>
      </div>      

      <div className="mt-auto pt-2 space-y-2">        {/* Bottom Action Buttons Container */}
        {/* Intro Tutorial Button - First Row */}
        <div className={getActionButtonContainerClasses()}>
          <Button
            variant="outline"
            className={`flex-1 group relative flex items-center justify-center border-2 transition-all duration-300 hover:scale-105 active:scale-95 ${getActionButtonClasses()} bg-gradient-to-r from-purple-600 to-pink-600 border-purple-600 shadow-lg shadow-purple-300 hover:from-purple-700 hover:to-pink-700 hover:border-purple-700 hover:shadow-xl hover:shadow-purple-400`}
            onClick={() => navigate('/intro-tutorial')}
          >
            <span className="transition-all duration-300 font-bold text-white text-lg sm:text-xl">
              App Tutorial
            </span>
          </Button>
        </div>

        {/* Analyze My Chat Button - Second Row */}
        <div className={getActionButtonContainerClasses()}>
          <Button
            variant="outline"
            className={`flex-1 group relative flex items-center justify-center border-2 transition-all duration-300 hover:scale-105 active:scale-95 ${getActionButtonClasses()} bg-gradient-to-r from-blue-600 to-purple-700 border-blue-600 shadow-lg shadow-blue-300 hover:from-blue-700 hover:to-purple-800 hover:border-blue-700 hover:shadow-xl hover:shadow-blue-400`}
            onClick={() => navigate('/tutorial')}
          >
            <span className="transition-all duration-300 font-bold text-white text-lg sm:text-xl">
              Analyze My Chat
            </span>
          </Button>
        </div>

        {/* History and Demo Buttons - Smaller Secondary Row */}
        <div className={`${shouldShowDemo ? 'grid grid-cols-3' : 'flex'} gap-2 mx-auto justify-center items-center mt-4 ${getActionButtonContainerClasses().replace('flex gap-2', '').replace('gap-3', '').replace('gap-4', '')}`}>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 group relative flex items-center justify-center gap-1.5 px-3 py-2 h-10 rounded-lg text-sm transition-all duration-200 hover:scale-102 active:scale-98 bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300"
            onClick={() => navigate('/history')}
          >
            <span className="font-medium text-gray-600 group-hover:text-gray-800 transition-colors">
              History
            </span>
          </Button>
          
          {/* Demo Data Button - Always visible when shouldShowDemo is true */}
          {shouldShowDemo && (
            <Button
              variant="outline"
              size="sm"
              className="flex-1 group relative flex items-center justify-center gap-1.5 px-3 py-2 h-10 rounded-lg text-sm transition-all duration-200 hover:scale-102 active:scale-98 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 hover:bg-gradient-to-r hover:from-emerald-100 hover:to-green-100 hover:border-emerald-300"
              onClick={async () => {
                try {
                  hapticFeedback.buttonPress();
                  console.log('Loading demo data...');
                  
                  const apiAnalysisType = chatType === 'one-on-one' ? 'love' : 'group';
                  setAnalysisData({
                    source: 'mock',
                    fileName: 'demo_chat.txt',
                    text: 'Demo chat text for demonstration purposes...',
                    result: demoData,
                    selectedChatType: chatType,
                    analysisType: apiAnalysisType
                  });

                  setState(AnalysisState.DASHBOARD);

                  // Navigate based on the selected chat type
                  if (chatType === 'one-on-one') {
                    navigate('/relationship-type', { replace: true });
                  } else {
                    navigate('/', { replace: true });
                  }
                } catch (error) {
                  console.error('Error loading demo data:', error);
                }
              }}
            >
              <span className="font-medium text-emerald-600 group-hover:text-emerald-700 transition-colors">
                Demo Data
              </span>
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            disabled={!hasHistory}
            className={`flex-1 group relative flex items-center justify-center gap-1.5 px-3 py-2 h-10 rounded-lg text-sm transition-all duration-200 ${
              hasHistory 
                ? 'hover:scale-102 active:scale-98 bg-white border border-gray-200 hover:bg-gray-50 hover:border-gray-300' 
                : 'bg-gray-100 border border-gray-200 text-gray-400 cursor-not-allowed opacity-50'
            }`}
            onClick={async () => {
              if (!hasHistory) return;
              
              try {
                // Get saved analyses using the same function as AnalysisHistory
                const savedAnalyses = await getSavedAnalyses();
                
                if (savedAnalyses.length > 0) {
                  // Get the most recent analysis (last in array)
                  const lastAnalysis = savedAnalyses[0];
                  
                  // Check if it has the required data
                  if (lastAnalysis.analysisJson && lastAnalysis.analysisType && lastAnalysis.selectedChatType) {
                    // Set loading from history flag
                    setIsLoadingFromHistory(true);
                    
                    const mappedAnalysisData = {
                      source: 'history' as const,
                      fileName: lastAnalysis.originalFileName,
                      result: lastAnalysis.analysisJson,
                      selectedChatType: lastAnalysis.selectedChatType,
                      analysisType: lastAnalysis.analysisType,
                    };

                    // Set the analysis data in context
                    setAnalysisData(mappedAnalysisData);

                    // Set the app state to DASHBOARD
                    setState(AnalysisState.DASHBOARD);

                    // Navigate based on analysis type
                    if (lastAnalysis.analysisType === 'love' || lastAnalysis.selectedChatType === 'one-on-one') {
                      navigate('/one-on-one?state=DASHBOARD');
                    } else {
                      navigate('/?state=DASHBOARD');
                    }

                    // Clear the loading flag after navigation
                    setTimeout(() => {
                      setIsLoadingFromHistory(false);
                    }, 1000);
                    
                    return;
                  }
                }
                
                // No valid history found - navigate to history page or show message
                navigate('/tutorial');
                
              } catch (error) {
                console.error('Error loading last analysis:', error);
                
                // Fallback to demo data
                setAnalysisData({
                  source: 'mock',
                  fileName: 'demo_chat.txt', 
                  text: 'Demo chat text for demonstration purposes...',
                  result: demoData,
                  selectedChatType: 'group',
                  analysisType: 'group'
                });
                
                setState(AnalysisState.DASHBOARD);
                navigate('/', { replace: true });
              }
            }}
          >
            <span className={`font-medium transition-colors ${
              hasHistory 
                ? 'text-gray-600 group-hover:text-gray-800' 
                : 'text-gray-400'
            }`}>
              Last Analysis
            </span>
          </Button>
        </div>


      </div>

      {/* Emoji Burst Animation Overlay */}
      <AnimatePresence>
        {emojiParticles.map((particle) => (
          <motion.div
            key={particle.id}
            className="fixed pointer-events-none z-50 text-2xl sm:text-3xl md:text-4xl"
            initial={{
              x: particle.startX,
              y: particle.startY,
              scale: 0,
              opacity: 0,
            }}
            animate={{
              x: [
                particle.startX,
                particle.centerX, // Pass exactly through center (no random offset)
                particle.centerX + (Math.random() - 0.5) * 120, // Spread out only for final upward movement
              ],
              y: [
                particle.startY,
                particle.centerY, // Pass through exact center Y
                particle.centerY - 400 - Math.random() * 200, // Fly high upward from center
              ],
              scale: [0, 1.2, 0],
              opacity: [0, 1, 0],
              rotate: [0, 45, 90], // Gentle rotation instead of flipping upside down
            }}
            transition={{
              duration: 1.8,
              delay: particle.delay,
              ease: "easeOut", // Smooth continuous motion
              times: [0, 0.3, 1], // Quick transition through middle, no pause
            }}
            exit={{
              opacity: 0,
              scale: 0,
            }}
          >
            {particle.emoji}
          </motion.div>
        ))}
      </AnimatePresence>
      </div>
    </>
  );
};

export default UploadSection;
