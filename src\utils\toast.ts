/**
 * Smart toast management with production mode awareness and haptic feedback
 */
import { toast as sonnerToast } from 'sonner';
import { isProductionMode, isDevelopmentMode } from './environment';
import { toastHaptics } from './haptics';

/**
 * Toast configuration for production control
 */
const TOAST_CONFIG = {
  // Set which toasts to allow in production
  ALLOW_IN_PRODUCTION: {
    PURCHASE_PROCESSING: false, // Purchase processing toasts
    ALL_TOASTS: false,          // Set to true to enable all toasts in production
    SUBSCRIPTION_VERIFICATION: false, // Subscription limit toasts
    SUCCESS_MESSAGES: false,    // Success notifications
    ERROR_MESSAGES: false,      // Error notifications
    INFO_MESSAGES: false,       // Info notifications
    WARNING_MESSAGES: false,    // Warning notifications
    FEEDBACK_MESSAGES: true,    // Feedback sent notifications (only allowed in production)
  }
};

/**
 * Helper function to check if a toast should be shown in production
 */
const shouldShowInProduction = (toastType: keyof typeof TOAST_CONFIG.ALLOW_IN_PRODUCTION, message?: string): boolean => {
  if (isDevelopmentMode()) return true;
  
  // If all toasts are enabled in production, show everything
  if (TOAST_CONFIG.ALLOW_IN_PRODUCTION.ALL_TOASTS) return true;
  
  // Special case for purchase processing
  if (toastType === 'PURCHASE_PROCESSING' && message === 'Processing purchase...') {
    return TOAST_CONFIG.ALLOW_IN_PRODUCTION.PURCHASE_PROCESSING;
  }
  
  return TOAST_CONFIG.ALLOW_IN_PRODUCTION[toastType];
};

/**
 * Smart toast wrapper that handles production vs development messaging
 */
export const toast = {
  /**
   * Error messages
   */
  error: (userMessage: string, technicalDetails?: string) => {
    if (shouldShowInProduction('ERROR_MESSAGES')) {
      toastHaptics.error();
      const fullMessage = isDevelopmentMode() && technicalDetails 
        ? `${userMessage} (Debug: ${technicalDetails})` 
        : userMessage;
      sonnerToast.error(fullMessage);
    }
  },

  /**
   * Success messages
   */
  success: (message: string) => {
    if (shouldShowInProduction('SUCCESS_MESSAGES')) {
      toastHaptics.success();
      sonnerToast.success(message);
    }
  },

  /**
   * Warning messages
   */
  warning: (userMessage: string, technicalDetails?: string) => {
    if (shouldShowInProduction('WARNING_MESSAGES')) {
      toastHaptics.warning();
      const fullMessage = isDevelopmentMode() && technicalDetails 
        ? `${userMessage} (Debug: ${technicalDetails})` 
        : userMessage;
      sonnerToast.warning(fullMessage);
    }
  },

  /**
   * Info messages
   */
  info: (message: string) => {
    if (shouldShowInProduction('INFO_MESSAGES')) {
      toastHaptics.info();
      sonnerToast.info(message);
    }
  },

  /**
   * Loading messages (supports purchase processing control)
   */
  loading: (message: string, options?: { id?: string }) => {
    if (shouldShowInProduction('PURCHASE_PROCESSING', message)) {
      toastHaptics.info();
      sonnerToast.loading(message, options);
    }
  },

  /**
   * Dismiss toasts - always works
   */
  dismiss: (id?: string) => {
    sonnerToast.dismiss(id);
  },

  /**
   * Debug messages - development only
   */
  debug: (message: string) => {
    if (isDevelopmentMode()) {
      toastHaptics.info();
      sonnerToast.info(`[Debug] ${message}`);
    }
  },

  /**
   * Development-only messages
   */
  dev: (message: string) => {
    if (isDevelopmentMode()) {
      toastHaptics.info();
      sonnerToast.info(`[Dev] ${message}`);
    }
  },

  /**
   * Quota/subscription related errors
   */
  quota: (message?: string) => {
    if (shouldShowInProduction('SUBSCRIPTION_VERIFICATION')) {
      toastHaptics.warning();
      const userMessage = isProductionMode() 
        ? 'Analysis limit reached. Please upgrade to continue analyzing more chats.'
        : message || 'Quota exceeded';
      sonnerToast.error(userMessage);
    }
  },

  /**
   * Network/connection errors
   */
  network: (technicalMessage?: string) => {
    if (shouldShowInProduction('ERROR_MESSAGES')) {
      toastHaptics.error();
      const userMessage = isProductionMode()
        ? 'Connection issue. Please check your internet and try again.'
        : technicalMessage || 'Network error';
      sonnerToast.error(userMessage);
    }
  },

  /**
   * File upload related errors
   */
  fileError: (userMessage: string, technicalDetails?: string) => {
    if (shouldShowInProduction('ERROR_MESSAGES')) {
      toastHaptics.error();
      const fullMessage = isDevelopmentMode() && technicalDetails 
        ? `${userMessage} (Debug: ${technicalDetails})` 
        : userMessage;
      sonnerToast.error(fullMessage);
    }
  },

  /**
   * Analysis related success messages
   */
  analysisSuccess: (message: string = 'Analysis completed successfully!') => {
    if (shouldShowInProduction('SUCCESS_MESSAGES')) {
      toastHaptics.success();
      sonnerToast.success(message);
    }
  },

  /**
   * Analysis related error messages
   */
  analysisError: (userMessage: string, technicalDetails?: string) => {
    if (shouldShowInProduction('ERROR_MESSAGES')) {
      toastHaptics.error();
      const message = isDevelopmentMode() && technicalDetails 
        ? `${userMessage} (Debug: ${technicalDetails})`
        : userMessage;
      sonnerToast.error(message);
    }
  },

  /**
   * Feedback success with 2-second duration
   */
  feedbackSuccess: (message: string = 'Thank you for your feedback!') => {
    if (shouldShowInProduction('FEEDBACK_MESSAGES')) {
      toastHaptics.success();
      sonnerToast.success(message, { duration: 2000 });
    }
  }
};

/**
 * Production control functions for easy toast management
 */
export const toastControls = {
  /**
   * Enable all toasts in production
   */
  enableAllInProduction: () => {
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.ALL_TOASTS = true;
  },

  /**
   * Disable all toasts in production (except what's explicitly enabled)
   */
  disableAllInProduction: () => {
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.ALL_TOASTS = false;
  },

  /**
   * Enable only feedback toasts in production
   */
  enableOnlyFeedbackToasts: () => {
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.ALL_TOASTS = false;
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.PURCHASE_PROCESSING = false;
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.SUBSCRIPTION_VERIFICATION = false;
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.SUCCESS_MESSAGES = false;
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.ERROR_MESSAGES = false;
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.INFO_MESSAGES = false;
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.WARNING_MESSAGES = false;
    TOAST_CONFIG.ALLOW_IN_PRODUCTION.FEEDBACK_MESSAGES = true;
  },

  /**
   * Get current toast configuration
   */
  getCurrentConfig: () => ({ ...TOAST_CONFIG.ALLOW_IN_PRODUCTION }),
};

/**
 * Legacy toast export for backward compatibility
 * Gradually replace direct sonner imports with this smart wrapper
 */
export { toast as smartToast };

/**
 * Quick production mode check for inline usage
 */
export const showToastInProduction = (message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') => {
  if (isProductionMode()) {
    return; // Don't show debug toasts in production
  }
  
  switch (type) {
    case 'success':
      toast.success(message);
      break;
    case 'warning':
      toast.warning(message);
      break;
    case 'error':
      toast.error(message);
      break;
    default:
      toast.info(message);
  }
};