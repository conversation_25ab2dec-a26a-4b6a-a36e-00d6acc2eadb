import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import AppLayout from '../components/AppLayout';
import UploadSection from '../components/UploadSection';
import InterstitialLoader from '../components/InterstitialLoader';
import DashboardWrapper from '../components/DashboardWrapper';
import DuplicateChatModal from '../components/DuplicateChatModal';
import { AnalysisState, useAppState } from '../contexts/AppStateContext';
import { useAnalysis } from '../contexts/AnalysisContext';
import { analyzeChatText, analyzeChatFile } from '../services/api';
import { saveAnalysis, getAnalysisById, AnalysisEntry } from '../utils/analysisStorage';
import { isGroupChat } from '../utils/chatTypeDetector';
import { isDevelopmentMode } from '../utils/environment';
import { toast } from '../utils/toast';
import { hapticFeedback } from '../utils/haptics';
import { generateChatHash, findAnalysisByHash, storeChatHash, extractFirstTenLines } from '../utils/chatHashUtils';
import { extractMessageCount, extractParticipantCount } from '../utils/metadataExtraction';
import { Button } from '../components/ui/button';

const Index = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { state, setState, showDailyLimitDialog, showSubscriptionModal, hasFileBeenProcessed, setFileProcessed } = useAppState();
  const { analysisData, setAnalysisData } = useAnalysis();
  const [chatText, setChatText] = React.useState<string>('');
  const [chatTitle, setChatTitle] = React.useState<string>('');
  const [chatPlatform, setChatPlatform] = React.useState<string>('whatsapp');
  // State for duplicate detection
  const [showDuplicateModal, setShowDuplicateModal] = React.useState<boolean>(false);
  const [duplicateAnalysis, setDuplicateAnalysis] = React.useState<AnalysisEntry | null>(null);
  const [pendingChatText, setPendingChatText] = React.useState<string>('');
  const [pendingFirstTenLines, setPendingFirstTenLines] = React.useState<string>('');
  // State for loading and error handling is managed by the API service

  // Save current analysis to storage
  const saveCurrentAnalysis = async () => {
    console.log('⚡️  [log] - saveCurrentAnalysis called with:', {
      chatTextLength: chatText?.length || 0,
      hasAnalysisData: !!analysisData,
      analysisDataKeys: analysisData ? Object.keys(analysisData) : [],
      hasResult: !!analysisData?.result,
      resultKeys: analysisData?.result ? Object.keys(analysisData.result) : [],
      analysisType: analysisData?.analysisType,
      selectedChatType: analysisData?.selectedChatType
    });
    
    if (!analysisData?.result) {
      console.log('⚡️  [log] - No analysis data to save');
      return;
    }

    try {
      // Extract metadata from chat text
      const messageCount = extractMessageCount(chatText);
      const participantCount = extractParticipantCount(chatText);
      
      // Generate hash for the chat
      const chatHash = await generateChatHash(chatText);
      const firstTenLines = extractFirstTenLines(chatText);
      
      const analysisEntry = {
        title: chatTitle || analysisData.fileName || 'Chat Analysis',
        analysisJson: analysisData.result,
        analysisType: analysisData.analysisType || 'group',
        selectedChatType: analysisData.selectedChatType || 'group',
        originalFileName: analysisData.fileName,
        messageCount: messageCount,
        participants: participantCount,
        chatHash: chatHash
      };
      
      const savedAnalysis = await saveAnalysis(analysisEntry);
      
      // Store the hash mapping
      await storeChatHash(chatHash, savedAnalysis.id, firstTenLines);
      
      console.log('Analysis saved successfully with hash:', chatHash);
      if (isDevelopmentMode()) {
        hapticFeedback.success();
        toast.dev('Analysis saved to history');
      }
    } catch (error) {
      console.error('Failed to save analysis:', error);
      if (isDevelopmentMode()) {
        hapticFeedback.error();
        toast.dev('Failed to save analysis');
      }
    }
  };

  // Handlers for duplicate detection modal
  const handleViewAnalysis = () => {
    if (!duplicateAnalysis) return;
    
    // Create analysis data from the existing analysis
    const mappedAnalysisData = {
      source: 'history' as const,
      fileName: duplicateAnalysis.originalFileName,
      result: duplicateAnalysis.analysisJson,
      selectedChatType: duplicateAnalysis.selectedChatType,
      analysisType: duplicateAnalysis.analysisType,
    };
    
    // Set the analysis data in context
    setAnalysisData(mappedAnalysisData);
    
    // Close modal
    setShowDuplicateModal(false);
    setDuplicateAnalysis(null);
    
    // Navigate to appropriate page based on analysis type
    if (duplicateAnalysis.analysisType === 'love' || duplicateAnalysis.selectedChatType === 'one-on-one') {
      navigate('/one-on-one', { replace: true });
    } else {
      setState(AnalysisState.DASHBOARD);
    }
  };

  const handleGoToHistory = () => {
    setShowDuplicateModal(false);
    setDuplicateAnalysis(null);
    navigate('/history');
  };

  const handleAnalyzeAnyway = () => {
    // Close modal and proceed with analysis using pending chat text
    setShowDuplicateModal(false);
    setDuplicateAnalysis(null);
    
    // Proceed with analysis of the pending text
    const textToAnalyze = pendingChatText;
    const titleToUse = chatTitle;
    const platformToUse = chatPlatform;
    
    // Clear pending state
    setPendingChatText('');
    setPendingFirstTenLines('');
    
    // Continue with analysis (bypass duplicate check this time)
    proceedWithAnalysis(textToAnalyze, titleToUse, platformToUse);
  };

  const handleCloseDuplicateModal = () => {
    setShowDuplicateModal(false);
    setDuplicateAnalysis(null);
    setPendingChatText('');
    setPendingFirstTenLines('');
  };



  // Helper function to proceed with analysis (used when bypassing duplicate check)
  const proceedWithAnalysis = async (text: string, title?: string, platform?: string) => {
    setChatText(text);
    setChatTitle(title || 'Chat Analysis');
    setChatPlatform(platform || 'whatsapp');
    setState(AnalysisState.ANALYZING);

    // Check if we already have analysis data in the context (from FileHandler or History)
    if (analysisData && (analysisData.source === 'api' || analysisData.source === 'history')) {
      // ... (rest of the original handleAnalyze logic will go here)
      // For now, let's implement the basic API call
      try {
        console.log('Proceeding with new analysis despite duplicate');
        
        const result = await analyzeChatText(text, title || 'Chat Analysis', 'group');
        setAnalysisData(result);
        
        // Save the analysis (which will include hash generation)
        setTimeout(() => {
          saveCurrentAnalysis();
        }, 1000);
        
        // Navigate based on analysis type
        if (result.analysisType === 'love' || result.selectedChatType === 'one-on-one') {
          navigate('/one-on-one', { replace: true });
        } else {
          setState(AnalysisState.DASHBOARD);
        }
      } catch (error) {
        console.error('Analysis failed:', error);
        
        // Check if this is a quota/daily limit error
        const errorMessage = (error as Error)?.message?.toLowerCase() || '';
        const isQuotaError = errorMessage.includes('daily credit limit') || 
                            errorMessage.includes('daily limit') ||
                            errorMessage.includes('analysis limit reached') ||
                            errorMessage.includes('limit reached');

        if (isQuotaError) {
          console.log('Daily limit error detected, showing daily limit dialog');
          showDailyLimitDialog();
        } else {
          // For other errors, show generic error message
          toast.error((error as Error)?.message || 'Analysis failed. Please try again.');
        }
        
        setState(AnalysisState.UPLOAD);
      }
      return;
    }

    // For new analysis, call the API
    try {
      const result = await analyzeChatText(text, title || 'Chat Analysis', 'group');
      setAnalysisData(result);
      
      // Save the analysis immediately (which will include hash generation)
      console.log('⚡️  [log] - Saving analysis immediately after setAnalysisData');
      // Use a brief delay to ensure React state has been updated
      setTimeout(() => {
        saveCurrentAnalysis();
      }, 100);
      
      // Navigate based on analysis type
      if (result.analysisType === 'love' || result.selectedChatType === 'one-on-one') {
        navigate('/one-on-one', { replace: true });
      } else {
        setState(AnalysisState.DASHBOARD);
      }
    } catch (error) {
      console.error('Analysis failed:', error);
      
      // Check if this is a quota/daily limit error
      const errorMessage = (error as Error)?.message?.toLowerCase() || '';
      const isQuotaError = errorMessage.includes('daily credit limit') || 
                          errorMessage.includes('daily limit') ||
                          errorMessage.includes('analysis limit reached') ||
                          errorMessage.includes('limit reached');

      if (isQuotaError) {
        console.log('Daily limit error detected, showing daily limit dialog');
        showDailyLimitDialog();
      } else {
        // For other errors, show generic error message
        toast.error((error as Error)?.message || 'Analysis failed. Please try again.');
      }
      
      setState(AnalysisState.UPLOAD);
    }
  };

  // Always skip tutorial - set localStorage directly
  React.useEffect(() => {
    localStorage.setItem('hasSeenTutorial', 'true');
  }, []);

  // Update URL when state changes - with debounce to prevent infinite loops
  useEffect(() => {
    // Skip this effect if we're in the middle of a state transition
    if (state === AnalysisState.ANALYZING) {
      return;
    }

    // Preserve environment parameters (prod, dev) when updating state
    const urlParams = new URLSearchParams(location.search);
    const envParams = new URLSearchParams();
    
    // Preserve environment-related parameters
    if (urlParams.get('prod')) envParams.set('prod', urlParams.get('prod')!);
    if (urlParams.get('dev')) envParams.set('dev', urlParams.get('dev')!);
    
    // Add state parameters
    if (state === AnalysisState.DASHBOARD) {
      envParams.set('state', 'DASHBOARD');
    } else if (state === AnalysisState.TUTORIAL) {
      envParams.set('state', 'TUTORIAL');
    }
    // We're skipping ANALYZING state for URL updates to prevent loops

    // Build the final search string
    const finalSearch = envParams.toString();
    const currentUrl = finalSearch ? `/?${finalSearch}` : '/';
    const currentSearch = finalSearch ? `?${finalSearch}` : '';

    if (currentSearch !== location.search) {
      console.log(`Updating URL from ${location.search} to ${currentSearch}`);
      navigate(currentUrl, { replace: true });
    }
  }, [state, navigate]);

  // Helper function to check if an error is a daily limit error
  const isDailyLimitError = (error: any): boolean => {
    const errorMessage = error.message?.toLowerCase() || '';
    console.log('🔍 [Index] Checking if daily limit error:', errorMessage);
    console.log('🔍 [Index] Full error object:', error);
    
    const isDailyLimit = errorMessage.includes('daily credit limit') || 
                        errorMessage.includes('daily limit') ||
                        errorMessage.includes('daily credit limit reached') ||
                        errorMessage.includes('analysis quota exceeded') ||
                        errorMessage.includes('quota exceeded');
    
    console.log('🎯 [Index] Daily limit detection result:', isDailyLimit);
    return isDailyLimit;
  };

  // Check if we need to redirect to one-on-one analysis page
  useEffect(() => {
    // Only check when in dashboard state and we have analysis data
    if (state === AnalysisState.DASHBOARD && analysisData) {
      // If this is a 1-on-1 chat analysis, navigate to the one-on-one page
      if (analysisData.analysisType === 'love' || analysisData.selectedChatType === 'one-on-one') {
        console.log('Index: Detected 1-on-1 analysis in Dashboard state, redirecting to one-on-one page');
        navigate('/one-on-one', { replace: true });
      }
    }
  }, [state, analysisData, navigate]);

  const handleAnalyze = async (text: string, title?: string, platform?: string) => {
    setChatText(text);
    setChatTitle(title || 'Chat Analysis');
    setChatPlatform(platform || 'whatsapp');

    // If a file has been processed automatically, clear the existing analysis data
    // to ensure fresh analysis when user manually clicks "Analyze Chat"
    if (hasFileBeenProcessed && analysisData) {
      console.log('Index: Clearing existing file analysis data for fresh manual analysis');
      setAnalysisData(null);
      setFileProcessed(false);
    }

    // Check for duplicate chat before proceeding with analysis
    try {
      const chatHash = await generateChatHash(text);
      const existingHashEntry = await findAnalysisByHash(chatHash);
      
      if (existingHashEntry) {
        // Found a duplicate, get the analysis details
        const existingAnalysis = await getAnalysisById(existingHashEntry.analysisId);
        
        if (existingAnalysis) {
          console.log('Duplicate chat detected:', existingAnalysis.title);
          
          // Store pending data for potential "Analyze Anyway" action
          setPendingChatText(text);
          setPendingFirstTenLines(existingHashEntry.firstTenLines);
          setDuplicateAnalysis(existingAnalysis);
          setShowDuplicateModal(true);
          
          // Don't proceed with analysis yet
          return;
        }
      }
    } catch (error) {
      console.error('Error checking for duplicate chat:', error);
      // Continue with analysis if duplicate check fails
    }

    // Proceed with normal analysis
    setState(AnalysisState.ANALYZING);

    // Check if we already have analysis data in the context (from FileHandler or History)
    if (analysisData && (analysisData.source === 'api' || analysisData.source === 'history')) {
      console.log(`Using existing analysis data from context (source: ${analysisData.source})`);
      
      // If loading from history, we don't need to save again or do analysis type detection
      if (analysisData.source === 'history') {
        console.log('Loading from history - skipping analysis type detection and save');
        
        // For history data, directly navigate based on the stored analysis type
        if (analysisData.analysisType === 'love' || analysisData.selectedChatType === 'one-on-one') {
          console.log('Navigating to one-on-one page for history data');
          setTimeout(() => {
            navigate('/one-on-one', { replace: true });
          }, 100);
        } else {
          console.log('Setting state to DASHBOARD for history data');
          setTimeout(() => {
            setState(AnalysisState.DASHBOARD);
          }, 100);
        }
        return;
      }

      // Check if user made a choice on homepage first
      const storedAnalysisType = localStorage.getItem('selectedAnalysisType');
      let shouldNavigateToOneOnOne = false;

      if (storedAnalysisType && (storedAnalysisType === 'group' || storedAnalysisType === 'love')) {
        // Respect user's choice from homepage
        shouldNavigateToOneOnOne = storedAnalysisType === 'love';
        console.log(`Using stored user choice: ${storedAnalysisType}, navigating to ${shouldNavigateToOneOnOne ? '1-on-1' : 'group'}`);
      } else {
        // Only auto-detect if user hasn't made a choice
        const chatContent = analysisData.text || text;
        const isGroupChatResult = isGroupChat(chatContent);
        shouldNavigateToOneOnOne = !isGroupChatResult;
        console.log(`No user choice found, auto-detected: ${isGroupChatResult ? 'group' : '1-on-1'}`);
      }

      // For debugging, also get the participant count using the old method
      const participantSet = new Set<string>();
      const chatLines = (analysisData.text || text).split('\n');
      const messageLines = chatLines.filter((line: string) => line.includes(':'));
      messageLines.forEach((line: string) => {
        const match = line.match(/^([^:]+):/);
        if (match && match[1]) {
          participantSet.add(match[1].trim());
        }
      });
      const participants = Array.from(participantSet);
      const participantCount = participants.length;
      console.log(`Detected ${participantCount} participants in the chat (existing data):`, participants);

      // Save the analysis to local storage
      setTimeout(() => {
        saveCurrentAnalysis();
      }, 1000);

      // Navigate based on the determined chat type
      if (shouldNavigateToOneOnOne) {
        // For 1-on-1 chats, navigate to the relationship type questionnaire
        console.log('Navigating to relationship type questionnaire for 1-on-1 chat (existing data)');
        setTimeout(() => {
          navigate('/relationship-type');
        }, 100);
      } else {
        // For group chats, go to the dashboard
        console.log('Setting state to DASHBOARD for group chat (with delay for existing data)');
        setTimeout(() => {
          setState(AnalysisState.DASHBOARD);
        }, 100);
      }

      return;
    }

    // Otherwise, we need to analyze the text
    try {
      // Determine if this is a _chat.txt file (for better backend processing)
      const fileName = text.includes('[') && text.includes(']:') ? '_chat.txt' : 'chat.txt';

      // Create a file from the text
      const file = new File([text], fileName, { type: 'text/plain' });

      // Check if there's a stored analysis type in localStorage
      const storedAnalysisType = localStorage.getItem('selectedAnalysisType');

      // If there's a stored analysis type, use it
      // Otherwise, determine the analysis type based on the chat content
      let analysisType: 'group' | 'love';

      if (storedAnalysisType && (storedAnalysisType === 'group' || storedAnalysisType === 'love')) {
        analysisType = storedAnalysisType as 'group' | 'love';
        console.log(`Index: Using stored analysis type from localStorage (user's choice): ${analysisType}`);
      } else {
        // Only auto-detect if user hasn't made a choice on homepage
        console.log('Index: No stored analysis type from user choice, detecting chat type before sending to backend...');
        const isGroupChatResult = isGroupChat(text);
        analysisType = isGroupChatResult ? 'group' : 'love';
        console.log(`Index: Auto-detected chat type: ${isGroupChatResult ? 'group' : 'one-on-one'}, using analysis type: ${analysisType}`);
      }

      // Send the file to the backend for analysis with the appropriate analysis type
      console.log(`Index: Sending file to backend for analysis with type: ${analysisType}`);
      const result = await analyzeChatFile(file, analysisType);

      // Log the API result for debugging
      console.log('API result received:', result);
      console.log('API result structure:', {
        source: result.source,
        fileName: result.fileName,
        hasText: !!result.text,
        hasResult: !!result.result,
        resultKeys: result.result ? Object.keys(result.result) : []
      });

      // Store the analysis result in the context
      const analysisDataToStore = {
        source: result.source as 'api' | 'file' | 'mock',
        fileName: result.fileName,
        text: result.text || text,
        result: result.result
      };

      console.log('Storing analysis data:', analysisDataToStore);
      setAnalysisData(analysisDataToStore);

      // We already determined the chat type before sending to the backend,
      // but let's check again with the returned content for consistency
      const chatContent = result.text || text;

      // For debugging, also get the participant count using the old method
      const participantSet = new Set<string>();
      const chatLines = chatContent.split('\n');

      // Only process lines that look like messages (contain a colon)
      const messageLines = chatLines.filter((line: string) => line.includes(':'));

      // Extract sender names from message lines
      messageLines.forEach((line: string) => {
        const match = line.match(/^([^:]+):/);
        if (match && match[1]) {
          participantSet.add(match[1].trim());
        }
      });

      // Get the unique participants
      const participants = Array.from(participantSet);
      const participantCount = participants.length;

      console.log(`Detected ${participantCount} participants in the chat:`, participants);

      // Save the analysis to local storage
      setTimeout(() => {
        saveCurrentAnalysis();
      }, 1000);

      // Navigate based on the analysis type we determined earlier
      if (analysisType === 'love') {
        // For love analysis, navigate to the relationship type questionnaire
        console.log('Navigating to relationship type questionnaire for love analysis');
        setTimeout(() => {
          navigate('/relationship-type');
        }, 100);
      } else {
        // For group analysis, go to the dashboard
        console.log('Setting state to DASHBOARD for group analysis (with delay)');
        setTimeout(() => {
          setState(AnalysisState.DASHBOARD);
        }, 100);
      }
    } catch (err) {
      console.error('Error analyzing chat:', err);

      // Handle subscription errors specifically
      if ((err as Error)?.name === 'SubscriptionRequiredError') {
        console.log('[Index] SubscriptionRequiredError caught');
        
        // Check if it's specifically a daily limit error
        if (isDailyLimitError(err)) {
          console.log('[Index] Daily limit error detected, showing daily limit dialog');
          showDailyLimitDialog();
        } else {
          console.log('[Index] General subscription error, showing subscription modal');
          
          // Create retry function for post-purchase action
          const retryAnalysis = async () => {
            try {
              console.log('[Index] Retrying analysis after subscription');
              setState(AnalysisState.ANALYZING);
              
              // Determine if this is a _chat.txt file (for better backend processing)
              const fileName = text.includes('[') && text.includes(']:') ? '_chat.txt' : 'chat.txt';
              const file = new File([text], fileName, { type: 'text/plain' });
              
              // Get stored analysis type or auto-detect
              const storedAnalysisType = localStorage.getItem('selectedAnalysisType');
              let analysisType: 'group' | 'love';
              
              if (storedAnalysisType && (storedAnalysisType === 'group' || storedAnalysisType === 'love')) {
                analysisType = storedAnalysisType as 'group' | 'love';
              } else {
                const isGroupChatResult = isGroupChat(text);
                analysisType = isGroupChatResult ? 'group' : 'love';
              }
              
              console.log(`[Index] Retrying with analysis type: ${analysisType}`);
              const result = await analyzeChatFile(file, analysisType);
              
              const analysisDataToStore = {
                source: result.source as 'api' | 'file' | 'mock',
                fileName: result.fileName,
                text: result.text || text,
                result: result.result
              };
              
              setAnalysisData(analysisDataToStore);
              
              // Save the analysis and navigate
              setTimeout(() => {
                saveCurrentAnalysis();
              }, 1000);
              
              if (analysisType === 'love') {
                console.log('[Index] Navigating to relationship type questionnaire after retry');
                setTimeout(() => {
                  navigate('/relationship-type');
                }, 100);
              } else {
                console.log('[Index] Setting state to DASHBOARD after retry');
                setTimeout(() => {
                  setState(AnalysisState.DASHBOARD);
                }, 100);
              }
            } catch (retryError) {
              console.error('[Index] Analysis retry failed:', retryError);
              setState(AnalysisState.UPLOAD);
              toast.error('Analysis failed. Please try again.');
            }
          };
          
          showSubscriptionModal(retryAnalysis);
        }
        return;
      }

      // Handle other errors
      hapticFeedback.analysisError();
      toast.error((err as Error)?.message || 'Analysis failed. Please try again.');

      // Return to main page after error - don't navigate to dashboard with mock data
      console.log('Index: Error occurred during analysis, returning to main page');
      setState(AnalysisState.UPLOAD);
    }
  };

  // The saveCurrentAnalysis function and its remnants are completely removed.

  return (
    <AppLayout>
      <div className="w-full h-full flex flex-col">
        {state === AnalysisState.UPLOAD && (
          <div className="flex-1 flex flex-col overflow-hidden">
            <UploadSection onAnalyze={handleAnalyze} />
          </div>
        )}

        {state === AnalysisState.ANALYZING && (
          <div className="flex-1 flex items-center justify-center">
            <InterstitialLoader />
          </div>
        )}

        {/* VibeMemeCard removed - going directly from analyzing to tutorial/dashboard */}


        {state === AnalysisState.DASHBOARD && (
          <div className="h-full overflow-auto">
            <DashboardWrapper chatText={chatText} />
          </div>
        )}

      </div>

      {/* Duplicate Chat Detection Modal */}
      {showDuplicateModal && duplicateAnalysis && (
        <DuplicateChatModal
          isOpen={showDuplicateModal}
          onClose={handleCloseDuplicateModal}
          existingAnalysis={duplicateAnalysis}
          firstTenLines={pendingFirstTenLines}
          onViewAnalysis={handleViewAnalysis}
          onGoToHistory={handleGoToHistory}
          onAnalyzeAnyway={handleAnalyzeAnyway}
        />
      )}
    </AppLayout>
  );
};

export default Index;
