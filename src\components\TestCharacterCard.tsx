import React from 'react';
import CharacterCard from './CharacterCard';

// Your JSON data
const anaData = {
  "name": "<PERSON>",
  "archetype": "Emonational Motivator",
  "emoji": "💖",
  "cardDescription": "<PERSON> is the heart of the group, offering support, advice, and affection to her children. She blends practical guidance with emotional warmth and consistently tries to include everyone.",
  "bgColor": "#F8BBD0",
  "favoriteTopics": [
    "Family well-being",
    "Food and cooking",
    "Support and advice",
    "Shopping",
    "Restaurant"
  ],
  "communicationStats": [
    {
      "title": "Message Count",
      "value": "248",
      "icon": "💬",
      "description": "Number of messages sent"
    },
    {
      "title": "Average Response Time",
      "value": "Varies",
      "icon": "⏱️",
      "description": "Response time fluctuates depending on the context"
    },
    {
      "title": "Use of Endearments",
      "value": "High",
      "icon": "🥰",
      "description": "Frequently uses terms of endearment like 'canim', 'aslanim', 'yavrum'"
    }
  ],
  "funnyQuotes": [
    "🤣",
    "Hahaha",
    "🤣\nSerseri"
  ],
  "mood": "Warm, supportive, and sometimes anxious",
  "traits": [
    {
      "name": "Caring",
      "value": 95,
      "color": "#E91E63"
    },
    {
      "name": "Advice-Giving",
      "value": 80,
      "color": "#9C27B0"
    },
    {
      "name": "Organized",
      "value": 70,
      "color": "#00BCD4"
    }
  ],
  "awards": [
    {
      "icon": "💖",
      "title": "Heart of the Group",
      "description": "For consistently providing emotional support and guidance to the group.",
      "color": "#E91E63"
    },
    {
      "icon": "👩‍🍳",
      "title": "Culinary Coordinator",
      "description": "For frequently suggesting meals, recipes, and organizing food-related plans.",
      "color": "#4CAF50"
    },
    {
      "icon": "🏡",
      "title": "The Logistics Manager",
      "description": "For her role in arranging transportation, veterinary appointments and social gatherings.",
      "color": "#FFC107"
    }
  ],
  "personalityMetrics": {
    "dryTexterScore": 5,
    "visualAbuserScore": 7,
    "spammerScore": 10,
    "conversationStarterScore": 10,
    "fastReplierScore": 1,
    "reactionMagnetScore": 3
  }
};

const TestCharacterCard = () => {
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Test Character Card with Real Data</h2>
      <CharacterCard
        name={anaData.name}
        archetype={anaData.archetype}
        emoji={anaData.emoji}
        description={anaData.cardDescription}
        bgColor={anaData.bgColor}
        apiPersonStats={anaData}
      />
    </div>
  );
};

export default TestCharacterCard;
