import * as Sentry from "@sentry/react";

export const testSentryIntegration = () => {
  try {
    // Test capturing an exception
    Sentry.captureException(new Error("Test Sentry Integration - This is a test error"));
    
    // Test capturing a message
    Sentry.captureMessage("Sentry integration test message", "info");
    
    // Test breadcrumb
    Sentry.addBreadcrumb({
      message: "Sentry test breadcrumb",
      level: "info",
      category: "test"
    });
    
    console.log("✅ Sentry test events sent successfully");
    return true;
  } catch (error) {
    console.error("❌ Failed to send Sentry test events:", error);
    return false;
  }
};

// Export for global access during development
if (import.meta.env.DEV) {
  (window as any).testSentry = testSentryIntegration;
}