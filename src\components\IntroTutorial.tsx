import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ArrowLeft, Sparkles, MessageSquare, Users, BarChart3, Heart, Zap, Shield } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import StaticHeader from './StaticHeader';
import { hapticFeedback } from '@/utils/haptics';

interface TutorialStep {
    id: number;
    icon: React.ReactNode;
    title: string;
    description: string;
    features?: string[];
    gradient: string;
    accentColor: string;
    screenshot?: string; // Optional screenshot path
}

const tutorialSteps: TutorialStep[] = [
    {
        id: 1,
        icon: <Sparkles className="w-12 h-12 text-white" />,
        title: 'Welcome to ChatBuster',
        description: 'Discover the hidden patterns, personalities, and dynamics in your chat conversations with AI-powered analysis.',
        features: [
            'Analyze group chat dynamics',
            'Discover member personalities',
            'Find hidden patterns',
            'Get insights into your relationships'
        ],
        gradient: 'from-purple-600 to-blue-600',
        accentColor: 'text-purple-600'
    },
    {
        id: 2,
        icon: <MessageSquare className="w-12 h-12 text-white" />,
        title: 'Main Action Buttons',
        description: 'Your main dashboard has three key buttons to get started. Click "Analyze My Chat" to upload a new chat, or use "History" and "Last Analysis" to access your previous analyses stored on your device.',
        features: [
            '📤 "Analyze My Chat" - Upload new chat file',
            '📚 "History" - View all previous analyses',
            '⏮️ "Last Analysis" - Quick access to recent analysis',
            '💾 All data saved locally on your device'
        ],
        gradient: 'from-blue-500 to-indigo-600',
        accentColor: 'text-blue-600',
        screenshot: '/intro_tutorial/analysis_history.png'
    },
    {
        id: 3,
        icon: <Users className="w-12 h-12 text-white" />,
        title: 'Member Insights',
        description: 'Get detailed personality profiles for each group member, including communication styles, activity patterns, and unique traits.',
        features: [
            'Personality analysis for each member',
            'Communication styles & patterns',
            'Activity levels & engagement',
            'Unique character traits & quirks'
        ],
        gradient: 'from-orange-500 to-red-500',
        accentColor: 'text-orange-600'
    },
    {
        id: 4,
        icon: <BarChart3 className="w-12 h-12 text-white" />,
        title: 'Social Dynamics',
        description: 'Visualize connections between members, identify cliques, and understand the social network within your group.',
        features: [
            'Social network mapping',
            'Clique detection & subgroups',
            'Interaction patterns analysis',
            'Connection strength visualization'
        ],
        gradient: 'from-blue-500 to-indigo-600',
        accentColor: 'text-blue-600'
    },
    {
        id: 5,
        icon: <Zap className="w-12 h-12 text-white" />,
        title: 'Chat Highlights',
        description: 'Discover the funniest moments, most engaging topics, and memorable messages that define your group\'s personality.',
        features: [
            'Funniest moments & jokes',
            'Most engaging conversation topics',
            'Memorable quotes & messages',
            'Interactive chat timeline'
        ],
        gradient: 'from-yellow-500 to-orange-500',
        accentColor: 'text-yellow-600'
    },
    {
        id: 6,
        icon: <Heart className="w-12 h-12 text-white" />,
        title: 'Love Chat Analysis (Coming Soon)',
        description: 'Exciting new feature in development! Love Chat will provide specialized analysis for romantic relationships in one-on-one conversations.',
        features: [
            'Attachment style analysis (coming soon)',
            'Love language detection (coming soon)',
            'Compatibility insights (coming soon)',
            'Relationship timeline & growth (coming soon)'
        ],
        gradient: 'from-pink-500 to-rose-600',
        accentColor: 'text-pink-600'
    },
    {
        id: 7,
        icon: <MessageSquare className="w-12 h-12 text-white" />,
        title: 'Getting Started',
        description: 'Ready to begin? Use the "Analyze My Chat" button to start your first analysis, or explore previous results with "History" and "Last Analysis".',
        features: [
            'Click "Analyze My Chat" to start',
            'Follow the simple upload tutorial',
            'Access saved analyses in History',
            'Quick access with Last Analysis'
        ],
        gradient: 'from-green-500 to-teal-600',
        accentColor: 'text-green-600'
    },
    {
        id: 8,
        icon: <BarChart3 className="w-12 h-12 text-white" />,
        title: '🚨 IMPORTANT: Navigation Tabs',
        description: 'After analysis completes, you MUST use the bottom navigation bar to explore all insights! Most users miss this - each tab reveals completely different analysis results.',
        features: [
            '🔥 Squad Vibes - Group overview & member stats',
            '🔗 Connections - Relationship network & interactions',
            '⚖️ Compare - Side-by-side member comparison',
            '⚠️ Don\'t miss out - tap every tab for full insights!'
        ],
        gradient: 'from-red-500 to-orange-500',
        accentColor: 'text-red-600',
        screenshot: '/intro_tutorial/navigation_tabs.png'
    },
    {
        id: 9,
        icon: <Users className="w-12 h-12 text-white" />,
        title: 'General Chat - All Types Supported',
        description: 'The General Chat analysis works with ANY conversation! Upload group chats OR individual one-on-one chats - both are fully supported.',
        features: [
            '👥 Group chats - Squad dynamics & member personalities',
            '� Individual chats - Personal conversation insights',
            '🤖 Automatic chat detection during upload',
            '⚙️ One analysis mode that handles all chat types'
        ],
        gradient: 'from-green-500 to-emerald-600',
        accentColor: 'text-green-600'
    },
    {
        id: 10,
        icon: <Shield className="w-12 h-12 text-white" />,
        title: 'Privacy & Security',
        description: 'Your privacy is our priority. All chat analysis happens locally on your device, and no data is stored permanently on our servers.',
        features: [
            '🔒 Local processing on your device',
            '🚫 No permanent data storage',
            '🛡️ End-to-end privacy protection',
            '⚡ Secure and fast analysis'
        ],
        gradient: 'from-blue-600 to-purple-600',
        accentColor: 'text-blue-600'
    },
    {
        id: 11,
        icon: <Sparkles className="w-12 h-12 text-white" />,
        title: 'Ready to Analyze?',
        description: 'You\'re all set! Start with "Analyze My Chat" to upload your own chat export, or try "Demo Data" to see ChatBuster in action with sample conversations.',
        features: [
            '🚀 Click "Analyze My Chat" to upload your chat',
            '🧪 Try "Demo Data" for an instant preview',
            '📱 Export without media for faster processing',
            '🎯 Discover amazing insights in seconds!'
        ],
        gradient: 'from-purple-600 to-pink-600',
        accentColor: 'text-purple-600'
    }
];

const IntroTutorial: React.FC = () => {
    const navigate = useNavigate();
    const [currentStep, setCurrentStep] = useState(0);
    const [screenHeight, setScreenHeight] = useState(window.innerHeight);
    const [touchStart, setTouchStart] = useState(0);
    const [touchEnd, setTouchEnd] = useState(0);

    // Update screen height on resize
    useEffect(() => {
        const handleResize = () => setScreenHeight(window.innerHeight);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Keyboard navigation
    useEffect(() => {
        const handleKeyPress = (e: KeyboardEvent) => {
            if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                e.preventDefault();
                prevStep();
            } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                e.preventDefault();
                nextStep();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                handleSkip();
            } else if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                if (isLastStep) {
                    handleGetStarted();
                } else {
                    nextStep();
                }
            }
        };

        window.addEventListener('keydown', handleKeyPress);
        return () => window.removeEventListener('keydown', handleKeyPress);
    }, [currentStep]);

    // Touch/swipe handling
    const handleTouchStart = (e: React.TouchEvent) => {
        setTouchEnd(0); // Reset touchEnd to prevent false swipe detection
        setTouchStart(e.targetTouches[0].clientX);
    };

    const handleTouchMove = (e: React.TouchEvent) => {
        setTouchEnd(e.targetTouches[0].clientX);
    };

    const handleTouchEnd = () => {
        if (!touchStart || !touchEnd) return;

        const distance = touchStart - touchEnd;
        const isLeftSwipe = distance > 50;
        const isRightSwipe = distance < -50;

        if (isLeftSwipe) {
            nextStep();
        } else if (isRightSwipe) {
            prevStep();
        }
    };

    // Dynamic sizing functions based on screen height
    const getContentClasses = () => {
        if (screenHeight <= 700) return 'px-4 py-2';
        if (screenHeight <= 800) return 'px-4 py-3';
        if (screenHeight <= 900) return 'px-6 py-4';
        return 'px-6 py-6';
    };

    const getTitleClasses = () => {
        if (screenHeight <= 700) return 'text-2xl';
        if (screenHeight <= 800) return 'text-2xl';
        if (screenHeight <= 900) return 'text-3xl';
        return 'text-3xl';
    };

    const getDescriptionClasses = () => {
        if (screenHeight <= 700) return 'text-sm';
        if (screenHeight <= 800) return 'text-base';
        return 'text-lg';
    };

    const getCardHeight = () => {
        // Increase height for steps with screenshots, but overall smaller due to compact header
        const hasScreenshot = currentStepData.screenshot;
        if (screenHeight <= 700) return hasScreenshot ? 'min-h-[450px]' : 'min-h-[400px]';
        if (screenHeight <= 800) return hasScreenshot ? 'min-h-[500px]' : 'min-h-[450px]';
        if (screenHeight <= 900) return hasScreenshot ? 'min-h-[550px]' : 'min-h-[500px]';
        return hasScreenshot ? 'min-h-[600px]' : 'min-h-[550px]';
    };

    const getIconContainerSize = () => {
        if (screenHeight <= 700) return 'w-20 h-20';
        if (screenHeight <= 800) return 'w-24 h-24';
        return 'w-28 h-28';
    };

    const getButtonClasses = () => {
        if (screenHeight <= 700) return 'h-8 px-3 text-xs';
        if (screenHeight <= 800) return 'h-9 px-4 text-sm';
        return 'h-10 px-5 text-sm';
    };

    const nextStep = () => {
        if (currentStep < tutorialSteps.length - 1) {
            hapticFeedback.buttonPress();
            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 0) {
            hapticFeedback.buttonPress();
            setCurrentStep(currentStep - 1);
        }
    };

    const goToStep = (stepIndex: number) => {
        if (stepIndex >= 0 && stepIndex < tutorialSteps.length) {
            hapticFeedback.selection();
            setCurrentStep(stepIndex);
        }
    };

    const handleGetStarted = () => {
        hapticFeedback.buttonPress();
        navigate('/', { replace: true });
    };

    const handleSkip = () => {
        hapticFeedback.buttonPress();
        navigate('/', { replace: true });
    };

    const currentStepData = tutorialSteps[currentStep];
    const isLastStep = currentStep === tutorialSteps.length - 1;

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50 relative overflow-hidden">
            {/* Header */}
            <StaticHeader
                leftContent={
                    <Button
                        variant="ghost"
                        size="icon"
                        className="h-14 w-14 sm:h-16 sm:w-16 rounded-full hover:bg-white/20 focus:ring-2 focus:ring-purple-400"
                        onClick={() => navigate('/')}
                    >
                        <ArrowLeft className="h-8 w-8 sm:h-9 sm:w-9 text-purple-700" />
                        <span className="sr-only">Back to Upload</span>
                    </Button>
                }
                rightContent={
                    <Button
                        variant="ghost"
                        className="text-purple-600 hover:text-purple-700 font-medium"
                        onClick={handleSkip}
                    >
                        Skip
                    </Button>
                }
            />

            {/* Main Content */}
            <div className="h-[calc(100vh-80px)] sm:h-[calc(100vh-88px)] flex flex-col">
                {/* Progress Bar */}
                <div className="px-6 py-4">
                    <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-500">
                            Page {currentStep + 1} of {tutorialSteps.length}
                        </span>
                        <span className="text-sm font-medium text-purple-600">
                            {Math.round(((currentStep + 1) / tutorialSteps.length) * 100)}%
                        </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                        <motion.div
                            className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${((currentStep + 1) / tutorialSteps.length) * 100}%` }}
                            transition={{ duration: 0.5, ease: "easeOut" }}
                        />
                    </div>
                </div>

                {/* Tutorial Content */}
                <div className={`flex-1 ${getContentClasses()}`}>
                    <div className="max-w-md mx-auto h-full">
                        {/* Static Card Container */}
                        <div
                            className={`bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden ${getCardHeight()} flex flex-col`}
                            onTouchStart={handleTouchStart}
                            onTouchMove={handleTouchMove}
                            onTouchEnd={handleTouchEnd}
                        >
                            <AnimatePresence mode="wait">
                                {/* Animated Header Content */}
                                <motion.div
                                    key={`header-${currentStep}`}
                                    initial={{ opacity: 0, y: -10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: 10 }}
                                    transition={{ duration: 0.3, ease: "easeOut" }}
                                    className={`bg-gradient-to-br ${currentStepData.gradient} px-4 py-3 flex items-center text-white relative overflow-hidden`}
                                >
                                    {/* Background Pattern */}
                                    <div className="absolute inset-0 opacity-10">
                                        <div className="absolute top-2 left-2 w-8 h-8 border border-white/20 rounded-full"></div>
                                        <div className="absolute bottom-2 right-2 w-6 h-6 border border-white/20 rounded-full"></div>
                                    </div>

                                    {/* Icon Container */}
                                    <motion.div
                                        initial={{ scale: 0, rotate: -180 }}
                                        animate={{ scale: 1, rotate: 0 }}
                                        transition={{ duration: 0.6, delay: 0.1, type: "spring" }}
                                        className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center relative z-10 mr-4 flex-shrink-0"
                                    >
                                        <div className="w-6 h-6 text-white">
                                            {React.cloneElement(currentStepData.icon as React.ReactElement, { className: "w-6 h-6 text-white" })}
                                        </div>
                                        {/* Special highlight for important navigation step */}
                                        {currentStep === 7 && (
                                            <motion.div
                                                initial={{ scale: 0 }}
                                                animate={{ scale: 1 }}
                                                transition={{ duration: 0.3, delay: 0.8 }}
                                                className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center"
                                            >
                                                <span className="text-xs font-bold text-black">!</span>
                                            </motion.div>
                                        )}
                                    </motion.div>

                                    {/* Title and Step Number */}
                                    <div className="flex-1 min-w-0">
                                        <motion.h2
                                            initial={{ opacity: 0, x: 10 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            transition={{ duration: 0.4, delay: 0.2 }}
                                            className="text-lg font-bold text-white mb-1 truncate"
                                        >
                                            {currentStepData.title}
                                        </motion.h2>
                                        <motion.div
                                            initial={{ opacity: 0, x: 10 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            transition={{ duration: 0.4, delay: 0.3 }}
                                            className="text-xs font-medium text-white/80"
                                        >
                                            Page {currentStep + 1} of {tutorialSteps.length}
                                        </motion.div>
                                    </div>
                                </motion.div>
                            </AnimatePresence>

                            <AnimatePresence mode="wait">
                                {/* Animated Content */}
                                <motion.div
                                    key={`content-${currentStep}`}
                                    initial={{ opacity: 0, x: 20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    exit={{ opacity: 0, x: -20 }}
                                    transition={{ duration: 0.25, ease: "easeOut" }}
                                    className="flex-1 flex flex-col"
                                >
                                    {/* Navigation Buttons - Directly under header */}
                                    <motion.div
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.3, delay: 0.1 }}
                                        className="flex items-center justify-between px-4 py-3 border-b border-gray-100"
                                    >
                                        {/* Previous Button */}
                                        <Button
                                            variant="outline"
                                            onClick={prevStep}
                                            disabled={currentStep === 0}
                                            className={`flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0 ${getButtonClasses()}`}
                                        >
                                            <ChevronLeft className="w-3 h-3" />
                                            Back
                                        </Button>

                                        {/* Next/Start Button */}
                                        <Button
                                            onClick={isLastStep ? handleGetStarted : nextStep}
                                            className={`flex items-center gap-1 bg-gradient-to-r ${currentStepData.gradient} hover:opacity-90 text-white border-0 flex-shrink-0 ${getButtonClasses()}`}
                                        >
                                            {isLastStep ? (
                                                <>
                                                    Start
                                                    <Sparkles className="w-3 h-3" />
                                                </>
                                            ) : (
                                                <>
                                                    Next
                                                    <ChevronRight className="w-3 h-3" />
                                                </>
                                            )}
                                        </Button>
                                    </motion.div>
                                    {/* Screenshot Section - Show if available */}
                                    {currentStepData.screenshot && (
                                        <div className="px-4 py-3 bg-gray-50">
                                            <motion.div
                                                initial={{ opacity: 0, scale: 0.9 }}
                                                animate={{ opacity: 1, scale: 1 }}
                                                transition={{ duration: 0.4, delay: 0.2 }}
                                                className="relative rounded-lg overflow-hidden shadow-md border border-gray-200"
                                            >
                                                <img
                                                    src={currentStepData.screenshot}
                                                    alt={`${currentStepData.title} Screenshot`}
                                                    className="w-full h-auto object-contain max-h-32"
                                                    onError={(e) => {
                                                        // Hide the screenshot container if image fails to load
                                                        e.currentTarget.parentElement?.parentElement?.style.setProperty('display', 'none');
                                                    }}
                                                />
                                                {/* Overlay to highlight it's a screenshot */}
                                                <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent pointer-events-none" />
                                            </motion.div>
                                            <p className="text-xs text-gray-500 text-center mt-2">App Screenshot</p>
                                        </div>
                                    )}

                                    {/* Content */}
                                    <div className={`flex-1 ${getContentClasses()} flex flex-col justify-between`}>
                                        <div>
                                            {/* Description */}
                                            <motion.p
                                                initial={{ opacity: 0, y: 10 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                transition={{ duration: 0.4, delay: 0.1 }}
                                                className={`${getDescriptionClasses()} text-gray-600 leading-relaxed text-center mb-6`}
                                            >
                                                {currentStepData.description}
                                            </motion.p>

                                            {/* Features List */}
                                            {currentStepData.features && (
                                                <motion.div
                                                    initial={{ opacity: 0, y: 20 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ duration: 0.4, delay: 0.2 }}
                                                    className="space-y-3"
                                                >
                                                    {currentStepData.features.map((feature, index) => (
                                                        <motion.div
                                                            key={feature}
                                                            initial={{ opacity: 0, x: -10 }}
                                                            animate={{ opacity: 1, x: 0 }}
                                                            transition={{ duration: 0.3, delay: 0.3 + (index * 0.05) }}
                                                            className="flex items-center space-x-3 bg-gray-50 rounded-xl p-3"
                                                        >
                                                            <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${currentStepData.gradient} flex-shrink-0`} />
                                                            <span className="text-sm font-medium text-gray-700 flex-1">
                                                                {feature}
                                                            </span>
                                                        </motion.div>
                                                    ))}
                                                </motion.div>
                                            )}
                                        </div>

                                        {/* Dots Indicator at bottom */}
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ duration: 0.4, delay: 0.5 }}
                                            className="flex justify-center pt-6 mt-auto w-full"
                                        >
                                            <div className="flex gap-2 justify-center overflow-hidden">
                                                {tutorialSteps.map((_, index) => (
                                                    <motion.button
                                                        key={index}
                                                        initial={{ scale: 0 }}
                                                        animate={{ scale: 1 }}
                                                        transition={{ duration: 0.2, delay: 0.6 + (index * 0.02) }}
                                                        onClick={() => goToStep(index)}
                                                        className={`w-2 h-2 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 flex-shrink-0 ${
                                                            index === currentStep 
                                                                ? `bg-gradient-to-r ${currentStepData.gradient} w-6` 
                                                                : 'bg-gray-300 hover:bg-gray-400'
                                                        }`}
                                                        aria-label={`Go to page ${index + 1}`}
                                                    />
                                                ))}
                                            </div>
                                        </motion.div>
                                    </div>
                                </motion.div>
                            </AnimatePresence>
                        </div>
                    </div>
                </div>
            </div>

            {/* Background Decoration */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
                <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-200 rounded-full opacity-20 blur-3xl" />
                <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-200 rounded-full opacity-20 blur-3xl" />
                <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full opacity-10 blur-3xl" />
            </div>
        </div>
    );
};

export default IntroTutorial;
