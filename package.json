{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bun install && node scripts/setup-live-reload.js && vite", "build": "vite build", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "cap:sync": "npx cap sync", "cap:sync:prod": "npm run build:prod && npx cap sync", "cap:build:ios:prod": "npm run build:prod && npx cap sync && npx cap build ios", "cap:build:android:prod": "npm run build:prod && npx cap sync && npx cap build android", "cap:open:ios": "npx cap open ios", "cap:open:android": "npx cap open android", "cap:live:ios": "npx cap run ios --live-reload", "cap:setup-live-reload": "node scripts/setup-live-reload.js", "cap:dev": "node scripts/setup-live-reload.js && npm run dev", "resources": "npx @capacitor/assets generate --iconBackgroundColor '#ffffff' --iconBackgroundColorDark '#000000' --splashBackgroundColor '#ffffff' --splashBackgroundColorDark '#000000'"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/core": "^7.2.0", "@capacitor/filesystem": "^7.0.1", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.2.0", "@capacitor/preferences": "^7.0.1", "@capacitor/screen-orientation": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capawesome-team/capacitor-file-opener": "^7.0.1", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-spring/web": "^10.0.1", "@revenuecat/purchases-capacitor": "^10.3.4", "@sentry/capacitor": "^2.0.0", "@sentry/react": "9.27.0", "@sentry/vite-plugin": "^3.6.0", "@tanstack/react-query": "^5.56.2", "@types/uuid": "^10.0.0", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "d3": "^7.9.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.11.3", "gsap": "^3.13.0", "howler": "^2.2.4", "html2canvas": "^1.4.1", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "input-otp": "^1.2.4", "jszip": "^3.10.1", "lottie-react": "^2.4.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^15.5.3", "react-player": "^2.16.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-spring": "^10.0.1", "react-transition-group": "^4.4.5", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tone": "^15.1.22", "use-sound": "^5.0.0", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.2.0", "@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "chokidar": "^4.0.3", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "ts-prune": "^0.10.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}, "trustedDependencies": ["@swc/core"]}