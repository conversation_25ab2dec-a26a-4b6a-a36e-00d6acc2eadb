
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, CreditCard, Bell, Settings } from 'lucide-react';
import { getLocalizedPricing } from '@/services/pricing';

const SubscriptionManagement = () => {
  const [localizedPrice, setLocalizedPrice] = useState('$4.99');

  useEffect(() => {
    const fetchPrice = async () => {
      const pricing = await getLocalizedPricing();
      setLocalizedPrice(pricing.price);
    };
    fetchPrice();
  }, []);

  // This would typically fetch the current subscription details from your backend
  const subscription = {
    status: 'Active',
    plan: 'Pro',
    nextBilling: '2025-05-26',
    paymentMethod: 'Visa ending in 4242',
    price: localizedPrice,
    period: 'week'
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h3 className="text-xl font-bold">Your Subscription</h3>
            <p className="text-sm text-muted-foreground">Manage your plan, billing and notifications</p>
          </div>
          <div className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            {subscription.status}
          </div>
        </div>

        <div className="grid gap-6">
          <div className="flex gap-4 items-start">
            <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <h4 className="font-medium mb-1">Billing cycle</h4>
              <p className="text-sm text-muted-foreground">Your next payment is on {subscription.nextBilling}</p>
              <Button variant="link" className="p-0 h-auto text-sm" onClick={() => alert('Change billing cycle')}>
                Change billing cycle
              </Button>
            </div>
          </div>

          <div className="flex gap-4 items-start">
            <CreditCard className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <h4 className="font-medium mb-1">Payment method</h4>
              <p className="text-sm text-muted-foreground">{subscription.paymentMethod}</p>
              <Button variant="link" className="p-0 h-auto text-sm" onClick={() => alert('Change payment method')}>
                Update payment method
              </Button>
            </div>
          </div>

          <div className="flex gap-4 items-start">
            <Bell className="h-5 w-5 text-muted-foreground mt-0.5" />
            <div>
              <h4 className="font-medium mb-1">Notifications</h4>
              <p className="text-sm text-muted-foreground">Get notified about your subscription changes.</p>
              <Button variant="link" className="p-0 h-auto text-sm" onClick={() => alert('Update notification settings')}>
                Configure notifications
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">Current Plan: {subscription.plan}</h3>
            <div className="flex items-baseline gap-1">
              <span className="text-2xl font-bold">{subscription.price}</span>
              <span className="text-sm text-muted-foreground">/{subscription.period}</span>
            </div>
          </div>

          <div className="flex flex-col gap-3">
            <Button variant="destructive" onClick={() => alert('Cancel subscription')}>
              Cancel Subscription
            </Button>
            <Button variant="outline" onClick={() => alert('Download receipts')}>
              Download Receipts
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SubscriptionManagement;
