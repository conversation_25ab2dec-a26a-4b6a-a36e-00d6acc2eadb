
import React, { useState, useEffect } from 'react';
import PresentationSlide from '../PresentationSlide';
import AnimatedText from '../AnimatedText';
import { Card } from '@/components/ui/card';
import { LockO<PERSON>, Diamond, BadgeDollarSign } from 'lucide-react';
import { getLocalizedPricing } from '@/services/pricing';

const PricingScreen = () => {
  const [localizedPrice, setLocalizedPrice] = useState('$4.99');

  useEffect(() => {
    const fetchPrice = async () => {
      const pricing = await getLocalizedPricing();
      setLocalizedPrice(pricing.price);
    };
    fetchPrice();
  }, []);

  return (
    <PresentationSlide bgGradient="linear-gradient(135deg, #000000 0%, #FFCB11 100%)">
      <div className="relative flex flex-col items-center justify-between h-full w-full text-center py-8">
        <AnimatedText 
          text="Level Up Your Story 💅"
          className="text-2xl font-bold text-white mb-4"
          type="slide"
        />
        
        <div className="space-y-4 w-full px-4">
          <div className="bg-white/20 backdrop-blur-sm p-4 rounded-xl flex items-center justify-between">
            <div className="flex items-center">
              <LockOpen className="w-5 h-5 text-white mr-2" />
              <AnimatedText 
                text="Basic"
                className="text-lg font-medium text-white"
                delay={200}
              />
            </div>
            <AnimatedText 
              text="Free"
              className="text-lg font-bold text-astro-coral"
              delay={300}
            />
          </div>
          
          <div className="bg-astro-coral/30 backdrop-blur-sm p-4 rounded-xl flex items-center justify-between border border-astro-coral">
            <div className="flex items-center">
              <Diamond className="w-5 h-5 text-white mr-2" />
              <AnimatedText 
                text="Premium"
                className="text-lg font-medium text-white"
                delay={400}
              />
            </div>
            <AnimatedText 
              text={localizedPrice}
              className="text-lg font-bold text-astro-coral"
              delay={500}
            />
          </div>
          
          <div className="space-y-2 mt-4">
            <AnimatedText 
              text="✨ Unlock Extra Features"
              className="text-sm font-medium text-white"
              delay={600}
            />
            <AnimatedText 
              text="🎨 Custom Story Themes"
              className="text-sm font-medium text-white"
              delay={700}
            />
            <AnimatedText 
              text="🎵 Background Music"
              className="text-sm font-medium text-white"
              delay={800}
            />
          </div>
        </div>
        
        <AnimatedText 
          text="#UnlockTheTea"
          className="text-sm text-astro-coral mt-4"
          delay={900}
        />
      </div>
    </PresentationSlide>
  );
};

export default PricingScreen;
