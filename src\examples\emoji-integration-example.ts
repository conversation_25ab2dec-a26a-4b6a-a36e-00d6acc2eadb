/**
 * Example of how to integrate your emoji analysis data with the GroupSummaryCard component
 *
 * This file shows how to structure your emoji analysis data to work with the emoji leaderboard
 */

// Your emoji analysis data structure (as provided)
export const yourEmojiAnalysisData = {
  "emojiAnalysis": {
    "totalEmojiCount": 502,
    "uniqueEmojiCount": 40,
    "mostUsedEmoji": {
      "emoji": "😭",
      "count": 159
    },
    "emojiUsagePerParticipant": {
      "Kucuk kafa": {
        "totalCount": 154,
        "uniqueCount": 21,
        "mostUsed": [
          {
            "emoji": "👍",
            "count": 55
          },
          {
            "emoji": "😎",
            "count": 14
          },
          {
            "emoji": "🔥",
            "count": 8
          },
          {
            "emoji": "😭",
            "count": 7
          },
          {
            "emoji": "✍",
            "count": 7
          }
        ],
        "emojiPerMessage": 0.056
      },
      "Arda": {
        "totalCount": 18,
        "uniqueCount": 2,
        "mostUsed": [
          {
            "emoji": "🇭",
            "count": 9
          },
          {
            "emoji": "🇹",
            "count": 9
          }
        ],
        "emojiPerMessage": 0.006151742993848257
      },
      "Ibo": {
        "totalCount": 165,
        "uniqueCount": 19,
        "mostUsed": [
          {
            "emoji": "🤣",
            "count": 84
          },
          {
            "emoji": "👍",
            "count": 29
          },
          {
            "emoji": "😭",
            "count": 14
          },
          {
            "emoji": "👌",
            "count": 6
          },
          {
            "emoji": "😎",
            "count": 6
          }
        ],
        "emojiPerMessage": 0.10597302504816955
      },
      "Huso": {
        "totalCount": 165,
        "uniqueCount": 8,
        "mostUsed": [
          {
            "emoji": "😭",
            "count": 138
          },
          {
            "emoji": "😎",
            "count": 8
          },
          {
            "emoji": "❤",
            "count": 6
          },
          {
            "emoji": "🥰",
            "count": 5
          },
          {
            "emoji": "😂",
            "count": 3
          }
        ],
        "emojiPerMessage": 0.08258258258258258
      },
      "Tuna": {
        "totalCount": 0,
        "uniqueCount": 0,
        "mostUsed": [],
        "emojiPerMessage": 0
      }
    },
    "topEmojis": [
      {
        "emoji": "😭",
        "count": 159
      },
      {
        "emoji": "🤣",
        "count": 84
      },
      {
        "emoji": "👍",
        "count": 84
      },
      {
        "emoji": "😎",
        "count": 28
      },
      {
        "emoji": "🫡",
        "count": 10
      },
      {
        "emoji": "🇭",
        "count": 9
      },
      {
        "emoji": "🇹",
        "count": 9
      },
      {
        "emoji": "😂",
        "count": 8
      },
      {
        "emoji": "🔥",
        "count": 8
      },
      {
        "emoji": "✍",
        "count": 7
      }
    ],
    "emojiDiversity": 0.0796812749003984
  }
};

// Your conversation stats data structure (as provided)
export const yourConversationStatsData = {
  "conversationStats": {
    "conversations": [], // Array of 166 conversations
    "summary": {
      "totalConversations": 166,
      "avgSwitches": 25.301204819277107,
      "avgMessages": 52.36144578313253,
      "avgLengthSeconds": 8315.93373493976,
      "avgResponseTimeSeconds": 410.96095604344237,
      "switchContributions": [] // Array of switch contributions
    },
    "participantResponseTimes": [
      {
        "person": "Arda",
        "avgResponseTimeSeconds": 229.38209432454036,
        "responseCount": 1251
      },
      {
        "person": "Kucuk kafa",
        "avgResponseTimeSeconds": 230.29740680713127,
        "responseCount": 1234
      },
      {
        "person": "Huso",
        "avgResponseTimeSeconds": 260.7238307349666,
        "responseCount": 898
      },
      {
        "person": "Ibo",
        "avgResponseTimeSeconds": 312.80541871921184,
        "responseCount": 812
      },
      {
        "person": "Tuna",
        "avgResponseTimeSeconds": 319.2,
        "responseCount": 5
      }
    ]
  }
};

// Example of how to integrate this data with your existing chatStats
export const integratedChatStatsExample = {
  // Your existing chatStats properties
  totalMessages: 1423,
  avgResponseTime: "2.5 min", // This will be calculated from conversationStats
  mostUsedEmoji: "😭", // This will be overridden by emojiAnalysis.mostUsedEmoji.emoji
  emojiCount: 502, // Updated from your data
  mood: "Chaotic",
  vibeDescription: "Chaos energy meets emotional damage.",
  mostLeftOnRead: "Jamie (23x)",
  topEmojiUser: "Huso (165x)", // Updated from your data
  mostActiveTexter: "Sam (513 msgs)",

  // Add your emoji analysis data here
  emojiAnalysis: yourEmojiAnalysisData.emojiAnalysis,

  // Add your conversation stats data here
  conversationStats: yourConversationStatsData.conversationStats,

  // This will be automatically extracted from conversationStats.participantResponseTimes
  participantResponseTimes: yourConversationStatsData.conversationStats.participantResponseTimes
};

// Example of how to use this in your component
export const exampleUsage = `
// In your ScrollableDashboard.tsx, make sure your analysisResult includes both structures:

const analysisResult = {
  // ... your other data
  emojiAnalysis: yourEmojiAnalysisData.emojiAnalysis,
  conversationStats: yourConversationStatsData.conversationStats
};

// The component will automatically extract the data and pass it to MobileGroupSummaryCard
`;

/**
 * How the leaderboards will work with your data:
 *
 * EMOJI LEADERBOARD:
 * 1. Shows the top 5 emojis from your topEmojis array: 😭, 🤣, 👍, 😎, 🫡
 * 2. The most used emoji (😭) appears in the overview tile
 * 3. The popup shows emojis without counts as per your preference
 * 4. The emoji dialog title uses 😭 as the emoji icon
 *
 * RESPONSE TIME LEADERBOARD:
 * 1. Shows response times from your conversationStats.participantResponseTimes
 * 2. Displays: Arda (3.82 min), Kucuk kafa (3.84 min), Huso (4.35 min), Ibo (5.21 min), Tuna (5.32 min)
 * 3. Uses the exact same calculation logic as CompareMembers component
 * 4. Average response time calculated from all participants: ~6.84 min
 *
 * The component will automatically:
 * - Use your emojiAnalysis.topEmojis for the emoji leaderboard
 * - Use your conversationStats.participantResponseTimes for response time leaderboard
 * - Calculate average response time from your data
 * - Fall back to mock data if your data isn't available
 */
