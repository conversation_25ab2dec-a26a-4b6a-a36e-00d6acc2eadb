PODS:
  - Capacitor (7.2.0):
    - Capacitor<PERSON>ordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorBrowser (7.0.1):
    - Capacitor
  - CapacitorCordova (7.2.0)
  - CapacitorFilesystem (7.0.1):
    - Capacitor
  - CapacitorHaptics (7.0.1):
    - Capacitor
  - CapacitorPreferences (7.0.1):
    - Capacitor
  - CapacitorScreenOrientation (7.0.1):
    - Capacitor
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - CapawesomeTeamCapacitorFileOpener (7.0.1):
    - Capacitor
  - PurchasesHybridCommon (13.36.0):
    - RevenueCat (= 5.28.1)
  - RevenueCat (5.28.1)
  - RevenuecatPurchasesCapacitor (10.3.4):
    - Capacitor
    - PurchasesHybridCommon (= 13.36.0)
  - Sentry/HybridSDK (8.51.1)
  - SentryCapacitor (2.0.0):
    - Capacitor
    - Sentry/HybridSDK (= 8.51.1)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../node_modules/@capacitor/browser`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorFilesystem (from `../../node_modules/@capacitor/filesystem`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorPreferences (from `../../node_modules/@capacitor/preferences`)"
  - "CapacitorScreenOrientation (from `../../node_modules/@capacitor/screen-orientation`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - "CapawesomeTeamCapacitorFileOpener (from `../../node_modules/@capawesome-team/capacitor-file-opener`)"
  - "RevenuecatPurchasesCapacitor (from `../../node_modules/@revenuecat/purchases-capacitor`)"
  - "SentryCapacitor (from `../../node_modules/@sentry/capacitor`)"

SPEC REPOS:
  trunk:
    - PurchasesHybridCommon
    - RevenueCat
    - Sentry

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../node_modules/@capacitor/browser"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorFilesystem:
    :path: "../../node_modules/@capacitor/filesystem"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorPreferences:
    :path: "../../node_modules/@capacitor/preferences"
  CapacitorScreenOrientation:
    :path: "../../node_modules/@capacitor/screen-orientation"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapawesomeTeamCapacitorFileOpener:
    :path: "../../node_modules/@capawesome-team/capacitor-file-opener"
  RevenuecatPurchasesCapacitor:
    :path: "../../node_modules/@revenuecat/purchases-capacitor"
  SentryCapacitor:
    :path: "../../node_modules/@sentry/capacitor"

SPEC CHECKSUMS:
  Capacitor: 03bc7cbdde6a629a8b910a9d7d78c3cc7ed09ea7
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorBrowser: 6299776d496e968505464884d565992faa20444a
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorFilesystem: e6261c410436f54908c11f94336c5b58286b1db0
  CapacitorHaptics: 1f1e17041f435d8ead9ff2a34edd592c6aa6a8d6
  CapacitorPreferences: 6c98117d4d7508034a4af9db64d6b26fc75d7b94
  CapacitorScreenOrientation: 6d989dff97440c7bfa78deee13952d7fb03c27f3
  CapacitorSplashScreen: 1d67815a422a9b61539c94f283c08ed56667c0fc
  CapawesomeTeamCapacitorFileOpener: ae3340a148c8947dda81ed2a8a600988667909b3
  PurchasesHybridCommon: 27d71a65d486bbfd6822a58c9802f7191e438c05
  RevenueCat: 3fafcf295834118ed6e6ec9388a20a4c9092b1e6
  RevenuecatPurchasesCapacitor: a916cdf5a6c65ad3c7d6fc203f75e731a514bc89
  Sentry: d950c807ffb8e966ebf019b546505a144d35d439
  SentryCapacitor: a93a3cb31f3cdddec514ae36988fcfb38dfbc2b5

PODFILE CHECKSUM: 09691bb4690461e8e9029e10ad08bb8b34dbf1c7

COCOAPODS: 1.16.2
