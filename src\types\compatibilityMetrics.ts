// Interface for compatibility metrics data from the backend

export interface MetricScore {
  score: number;
  reason: string;
}

export interface RelationshipMetrics {
  respect: MetricScore;
  intimacy: MetricScore;
  empathy: MetricScore;
  joke_energy: MetricScore;
  roast: MetricScore;
  active_time?: MetricScore;
  lifestyle?: MetricScore;
  trust?: MetricScore;
  support?: MetricScore;
  response_time?: MetricScore;
  reference_term?: MetricScore;
  message_uniqueness?: MetricScore;
  response_kindness?: MetricScore;
  [key: string]: MetricScore | string | undefined;
}

export interface CompatibilityMetrics {
  pair: string[];
  compatibility_score: number;
  relationship_title: string;
  metrics: RelationshipMetrics;
  insight_jokes: string;
  nicknames: string;
  summary: string;
}
