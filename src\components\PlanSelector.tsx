
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Check } from 'lucide-react';
import { getLocalizedPricing } from '@/services/pricing';

const PlanSelector = () => {
  const [billingInterval, setBillingInterval] = useState<'weekly' | 'annually'>('weekly');
  const [localizedPrice, setLocalizedPrice] = useState('$4.99');

  useEffect(() => {
    const fetchPrice = async () => {
      const pricing = await getLocalizedPricing();
      setLocalizedPrice(pricing.price);
    };
    fetchPrice();
  }, []);

  const plans = [
    {
      name: 'Pro',
      price: billingInterval === 'weekly' ? localizedPrice : localizedPrice.replace(/[\d.,]+/, (match) => (parseFloat(match) * 52).toFixed(2)),
      period: billingInterval === 'weekly' ? '/week' : '/year',
      description: 'Everything you need for your analysis',
      features: [
        'Unlimited premium insights',
        'Unlimited meme generation',
        'Full compatibility reports',
        'Exclusive group insights'
      ]
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-center gap-4 p-1">
        <RadioGroup
          defaultValue="weekly"
          onValueChange={(value) => setBillingInterval(value as 'weekly' | 'annually')}
          className="flex gap-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="weekly" id="weekly" />
            <Label htmlFor="weekly">Weekly</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="annually" id="annually" />
            <Label htmlFor="annually">Annually</Label>
          </div>
        </RadioGroup>
      </div>

      <div className="grid gap-4">
        {plans.map((plan) => (
          <Card key={plan.name} className="p-6 bg-gradient-to-br from-astro-coral/30 to-astro-lavender/30 backdrop-blur-sm border-2 border-astro-coral">
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-bold">{plan.name}</h3>
                <div className="flex items-baseline gap-1">
                  <span className="text-3xl font-bold">{plan.price}</span>
                  <span className="text-sm text-muted-foreground">{plan.period}</span>
                </div>
                <p className="text-sm text-muted-foreground mt-2">{plan.description}</p>
              </div>
              <ul className="space-y-2">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-center gap-2 text-sm">
                    <Check className="w-4 h-4 text-astro-coral" />
                    {feature}
                  </li>
                ))}
              </ul>
              <Button className="w-full bg-gradient-to-r from-astro-coral to-astro-lavender hover:opacity-90">
                Upgrade Now
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default PlanSelector;
