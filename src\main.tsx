import { createRoot } from 'react-dom/client'
import * as <PERSON><PERSON> from "@sentry/react";
import App from './App.tsx'
import './index.css'

Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN,
  release: "chat-vibe-analyzer@1.0.0",
  dist: "1",
  tracesSampleRate: import.meta.env.PROD ? 0.1 : 1.0,
  integrations: [
    Sentry.replayIntegration({
      maskAllText: false,
      blockAllMedia: false,
    }),
  ],
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,
  environment: import.meta.env.MODE,
});

createRoot(document.getElementById("root")!).render(<App />);
