
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { mockPersonStats } from '@/utils/personStats';
import { ChevronDown, ChevronUp, User, BarChart2 } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import FunnyMetricsChart from './FunnyMetricsChart';
import { hapticFeedback } from '@/utils/haptics';

// Define animation styles
const animationStyles = `
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideInDown {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideInLeft {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  @keyframes slideInRight {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  @keyframes slideInUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes growWidth {
    from { width: 0; }
  }

  /* New behind-the-scenes animations */
  @keyframes slideUpFromBehind {
    from {
      transform: translateY(20px);
      opacity: 0;
      max-height: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
      max-height: 500px;
    }
  }

  @keyframes slideDownBehind {
    from {
      transform: translateY(0);
      opacity: 1;
      max-height: 500px;
    }
    to {
      transform: translateY(20px);
      opacity: 0;
      max-height: 0;
    }
  }

  /* Animation classes */
  .slide-up-from-behind {
    animation: slideUpFromBehind 250ms ease-out forwards;
    transform-origin: top;
  }

  .slide-down-behind {
    animation: slideDownBehind 200ms ease-in forwards;
    transform-origin: top;
  }

  /* Smooth height transitions */
  .expandable-content {
    transition: max-height 250ms ease-out, opacity 250ms ease-out, transform 250ms ease-out;
  }
`;

interface CharacterCardProps {
  name: string;
  archetype: string;
  emoji: string;
  description: string;
  bgColor: string;
  index?: number; // For stacked card positioning
  apiPersonStats?: any; // Optional API person stats
  isExpanded?: boolean; // Control expanded state from parent
  onToggleExpand?: () => void; // Callback when expand/collapse is toggled
}

// Note: All enhanced data interfaces removed as they're not needed for this simplified component

// Note: Extended insights data removed as it's now shown in a separate component

const CharacterCard = ({
  name,
  archetype,
  emoji,
  description,
  bgColor,
  index = 0,
  apiPersonStats,
  isExpanded: controlledExpanded,
  onToggleExpand
}: CharacterCardProps) => {
  // Use internal state if not controlled from parent
  const [internalExpanded, setInternalExpanded] = useState(false);
  // State for active tab
  const [activeTab, setActiveTab] = useState('personality');
  // State for animation tracking
  const [animationState, setAnimationState] = useState<'collapsed' | 'expanding' | 'expanded' | 'collapsing'>('collapsed');

  // Use controlled state if provided, otherwise use internal state
  const isExpanded = controlledExpanded !== undefined ? controlledExpanded : internalExpanded;

  // Handle expand/collapse with animation states
  const handleToggleExpand = () => {
    hapticFeedback.light();
    if (isExpanded) {
      // Start collapsing animation
      setAnimationState('collapsing');
      // After animation completes, update the expanded state
      setTimeout(() => {
        if (onToggleExpand) {
          onToggleExpand();
        } else {
          setInternalExpanded(false);
        }
        setAnimationState('collapsed');
      }, 200); // Match the collapse animation duration
    } else {
      // Start expanding
      setAnimationState('expanding');
      if (onToggleExpand) {
        onToggleExpand();
      } else {
        setInternalExpanded(true);
      }
      // After animation completes, set to expanded
      setTimeout(() => {
        setAnimationState('expanded');
      }, 250); // Match the expand animation duration
    }
  };

  // Find matching mock data for this person
  const mockData = mockPersonStats.find(person => person.name === name) || mockPersonStats[0];

  // Merge API data with mock data to ensure all properties are available
  // This way we use API data when available but fall back to mock data for any missing properties
  // Create a properly formatted personStats object
  const personStats = apiPersonStats ? {
    ...mockData,  // Start with mock data as base
    ...apiPersonStats,  // Override with API data where available

    // Use cardDescription for description if available
    description: apiPersonStats.cardDescription || description,

    // Log the cardDescription field for debugging
    _cardDescription: apiPersonStats.cardDescription,

    // Ensure these critical arrays exist and have content
    stats: apiPersonStats.communicationStats || mockData.stats,

    // Handle traits specially - check for traits_numerical and numerical_traits fields
    // IMPORTANT: Check multiple possible trait fields from the API response
    traits: (() => {
      // First try the standard traits field
      if (apiPersonStats.traits && Array.isArray(apiPersonStats.traits) && apiPersonStats.traits.length > 0) {
        return apiPersonStats.traits;
      }

      // Then try traits_numerical field
      if (apiPersonStats.traits_numerical && Array.isArray(apiPersonStats.traits_numerical) && apiPersonStats.traits_numerical.length > 0) {
        return apiPersonStats.traits_numerical;
      }

      // Then try numerical_traits field
      if (apiPersonStats.numerical_traits && Array.isArray(apiPersonStats.numerical_traits) && apiPersonStats.numerical_traits.length > 0) {
        return apiPersonStats.numerical_traits;
      }

      // If traits exists but is not an array, try to convert it
      if (apiPersonStats.traits && typeof apiPersonStats.traits === 'object' && !Array.isArray(apiPersonStats.traits)) {
        // If it has a nested traits array
        if (apiPersonStats.traits.traits && Array.isArray(apiPersonStats.traits.traits)) {
          return apiPersonStats.traits.traits;
        }

        // If it's a single trait object, wrap it in an array
        if (apiPersonStats.traits.name && apiPersonStats.traits.value !== undefined) {
          return [apiPersonStats.traits];
        }

        // If it's an object with numeric keys, convert to array
        const possibleArray = Object.values(apiPersonStats.traits).filter(item =>
          item && typeof item === 'object' && 'name' in item && 'value' in item
        );

        if (possibleArray.length > 0) {
          return possibleArray;
        }
      }

      // Fall back to empty array if nothing else works
      return [];
    })(),

    // Handle favoriteTopics specially - check for different formats
    // IMPORTANT: Check multiple possible favoriteTopics formats from the API response
    favoriteTopics: (() => {
      // First try the standard favoriteTopics field
      if (apiPersonStats.favoriteTopics && Array.isArray(apiPersonStats.favoriteTopics) && apiPersonStats.favoriteTopics.length > 0) {
        return apiPersonStats.favoriteTopics;
      }

      // If favoriteTopics exists but is not an array, try to convert it
      if (apiPersonStats.favoriteTopics && typeof apiPersonStats.favoriteTopics === 'object' && !Array.isArray(apiPersonStats.favoriteTopics)) {
        // If it's an object with numeric keys, convert to array
        const possibleArray = Object.values(apiPersonStats.favoriteTopics).filter(item =>
          item && typeof item === 'string'
        );

        if (possibleArray.length > 0) {
          return possibleArray;
        }
      }

      // If favoriteTopics is a string, split it into an array
      if (apiPersonStats.favoriteTopics && typeof apiPersonStats.favoriteTopics === 'string') {
        return apiPersonStats.favoriteTopics.split(',').map((topic: string) => topic.trim());
      }

      // Fall back to empty array if nothing else works
      return [];
    })(),

    // Handle common phrases (replacing funnyQuotes) - try multiple possible field names
    commonPhrases: apiPersonStats.commonPhrases ||
                   apiPersonStats.funnyQuotes ||
                   apiPersonStats.memorable_moments ||
                   apiPersonStats.inside_jokes ||
                   [],

    // Other arrays with fallbacks
    awards: apiPersonStats.awards || [],

    // Use communicationStats directly
    communicationStats: apiPersonStats.communicationStats || [],

    // Ensure mood is available
    mood: apiPersonStats.mood || mockData.mood,

    // Log raw data for debugging
    _rawApiData: apiPersonStats
  } : mockData;

  // Log the source of personStats for debugging
  React.useEffect(() => {
    console.log(`CharacterCard for ${name} using ${apiPersonStats ? 'API' : 'mock'} data:`, personStats);

    // Log detailed information about the API data if available
    if (apiPersonStats) {
      // Check if traits is defined and is an array
      const traitsStatus = apiPersonStats.traits === undefined
        ? 'undefined'
        : apiPersonStats.traits === null
          ? 'null'
          : Array.isArray(apiPersonStats.traits)
            ? `Array with ${apiPersonStats.traits.length} items`
            : `Not an array: ${typeof apiPersonStats.traits}`;

      // Check if cardDescription is defined
      const cardDescriptionStatus = apiPersonStats.cardDescription === undefined
        ? 'undefined'
        : apiPersonStats.cardDescription === null
          ? 'null'
          : `String: "${apiPersonStats.cardDescription}"`;

      console.log(`API data for ${name}:`, {
        name: apiPersonStats.name,
        archetype: apiPersonStats.archetype,
        emoji: apiPersonStats.emoji,
        cardDescriptionStatus: cardDescriptionStatus,
        cardDescription: apiPersonStats.cardDescription,
        description: description,
        finalDescription: personStats.description,
        bgColor: apiPersonStats.bgColor,

        // Log detailed information about traits
        traitsStatus: traitsStatus,
        traitsRawData: apiPersonStats.traits,
        traitsIsArray: Array.isArray(apiPersonStats.traits),
        traitsType: typeof apiPersonStats.traits,

        // Log additional traits fields
        traits_numerical: apiPersonStats.traits_numerical,
        traits_numerical_isArray: Array.isArray(apiPersonStats.traits_numerical),
        traits_numerical_length: apiPersonStats.traits_numerical ?
          (Array.isArray(apiPersonStats.traits_numerical) ? apiPersonStats.traits_numerical.length : 'not an array') :
          'undefined',

        numerical_traits: apiPersonStats.numerical_traits,
        numerical_traits_isArray: Array.isArray(apiPersonStats.numerical_traits),
        numerical_traits_length: apiPersonStats.numerical_traits ?
          (Array.isArray(apiPersonStats.numerical_traits) ? apiPersonStats.numerical_traits.length : 'not an array') :
          'undefined',

        // Log detailed information about favoriteTopics
        favoriteTopicsRawData: apiPersonStats.favoriteTopics,
        favoriteTopicsIsArray: Array.isArray(apiPersonStats.favoriteTopics),
        favoriteTopicsType: typeof apiPersonStats.favoriteTopics,

        // Log other arrays
        commonPhrases: apiPersonStats.commonPhrases,
        awards: apiPersonStats.awards,
        communicationStats: apiPersonStats.communicationStats,

        // Log personalityMetrics specifically
        personalityMetrics: apiPersonStats.personalityMetrics,
        personalityMetricsType: typeof apiPersonStats.personalityMetrics,
        personalityMetricsKeys: apiPersonStats.personalityMetrics ? Object.keys(apiPersonStats.personalityMetrics) : 'undefined'
      });

      // Log the raw API data for debugging
      console.log(`Raw API data for ${name}:`, apiPersonStats);

      // Log the final traits data being used
      console.log(`Final traits data for ${name}:`, personStats.traits);

      // Log the final favoriteTopics data being used
      console.log(`Final favoriteTopics data for ${name}:`, personStats.favoriteTopics);

      // Log the final description being used
      console.log(`Final description for ${name}:`, personStats.description);

      // If traits is empty but should be available in the API data, log a warning with detailed info
      // Add null check to prevent "Cannot read properties of undefined (reading 'length')" error
      if (personStats.traits && personStats.traits.length === 0 && apiPersonStats.traits) {
        console.warn(`Warning: Traits array is empty for ${name} even though API data has traits property`);
        console.warn(`Traits data type for ${name}:`, typeof apiPersonStats.traits);
        console.warn(`Traits data value for ${name}:`, apiPersonStats.traits);

        // Also check for traits_numerical and numerical_traits
        if (apiPersonStats.traits_numerical || apiPersonStats.numerical_traits) {
          console.warn(`Found alternative traits data:`, {
            traits_numerical: apiPersonStats.traits_numerical,
            numerical_traits: apiPersonStats.numerical_traits
          });
        }
      }

      // Check if favoriteTopics is empty but should be available in the API data
      if (personStats.favoriteTopics && personStats.favoriteTopics.length === 0 && apiPersonStats.favoriteTopics) {
        console.warn(`Warning: FavoriteTopics array is empty for ${name} even though API data has favoriteTopics property`);
        console.warn(`FavoriteTopics data type for ${name}:`, typeof apiPersonStats.favoriteTopics);
        console.warn(`FavoriteTopics data value for ${name}:`, apiPersonStats.favoriteTopics);

        // If it's an object but not an array, try to convert it to an array
        if (typeof apiPersonStats.favoriteTopics === 'object' && !Array.isArray(apiPersonStats.favoriteTopics)) {
          console.warn(`Attempting to fix favoriteTopics data for ${name} by converting object to array`);

          try {
            // If it's an object with numeric keys, convert to array
            const possibleArray = Object.values(apiPersonStats.favoriteTopics).filter(item =>
              item && typeof item === 'string'
            );

            if (possibleArray.length > 0) {
              personStats.favoriteTopics = possibleArray;
              console.warn(`Fixed favoriteTopics data for ${name} by converting object to array:`, personStats.favoriteTopics);
            }
          } catch (error) {
            console.error(`Error trying to fix favoriteTopics data for ${name}:`, error);
          }
        }

        // If it's an object but not an array, try to convert it to an array
        if (typeof apiPersonStats.traits === 'object' && !Array.isArray(apiPersonStats.traits)) {
          console.warn(`Attempting to fix traits data for ${name} by converting object to array`);

          try {
            // Try to extract traits from the object if it has a nested structure
            if (apiPersonStats.traits.traits && Array.isArray(apiPersonStats.traits.traits)) {
              personStats.traits = apiPersonStats.traits.traits;
              console.warn(`Fixed traits data for ${name} by using nested traits array:`, personStats.traits);
            }
            // If it's an object with name/value/color properties, wrap it in an array
            else if (apiPersonStats.traits.name && apiPersonStats.traits.value !== undefined) {
              personStats.traits = [apiPersonStats.traits];
              console.warn(`Fixed traits data for ${name} by wrapping single trait in array:`, personStats.traits);
            }
            // If it's an object with numeric keys (like {0: {...}, 1: {...}}), convert to array
            else {
              const possibleArray = Object.values(apiPersonStats.traits).filter(item =>
                item && typeof item === 'object' && 'name' in item && 'value' in item
              );

              if (possibleArray.length > 0) {
                personStats.traits = possibleArray;
                console.warn(`Fixed traits data for ${name} by converting object with numeric keys to array:`, personStats.traits);
              }
            }
          } catch (error) {
            console.error(`Error trying to fix traits data for ${name}:`, error);
          }
        }
      }
    }
  }, [name, apiPersonStats, personStats]);

  // Character awards removed - shown in component below

  // Note: Power moves, weaknesses, and extended insights data removed
  // as they are now shown in a separate component

  // Add animation styles to the document
  React.useEffect(() => {
    // Create style element if it doesn't exist
    const styleId = 'character-card-animations';
    if (!document.getElementById(styleId)) {
      const styleElement = document.createElement('style');
      styleElement.id = styleId;
      styleElement.textContent = animationStyles;
      document.head.appendChild(styleElement);
    }

    // Cleanup on unmount
    return () => {
      const existingStyle = document.getElementById(styleId);
      if (existingStyle && document.querySelectorAll('[data-character-card]').length <= 1) {
        existingStyle.remove();
      }
    };
  }, []);

  // We only need the personality traits for this component  // Create funny metrics from personalityMetrics data (excluding dryTexterScore)
  const funnyMetrics = React.useMemo(() => {
    // Debug logging for personalityMetrics
    console.log(`PersonalityMetrics for ${name}:`, apiPersonStats?.personalityMetrics);
    console.log(`Full apiPersonStats for ${name}:`, apiPersonStats);

    // If we have API data with personalityMetrics, use it
    if (apiPersonStats?.personalityMetrics) {
      const metrics = [];
      const personalityData = apiPersonStats.personalityMetrics;

      // Map personalityMetrics to user-friendly names (excluding dryTexterScore)
      const metricMapping = {
        visualAbuserScore: 'Media Lover',
        spammerScore: 'Spammer',
        conversationStarterScore: 'Conversation Starter',
        fastReplierScore: 'Quick Responder',
        reactionMagnetScore: 'Reaction Magnet'
      };

      // Create metrics array from available data
      Object.entries(metricMapping).forEach(([key, displayName]) => {
        if (personalityData[key] !== undefined) {
          console.log(`Adding metric for ${name}: ${displayName} = ${personalityData[key]}`);
          metrics.push({
            name: displayName,
            value: personalityData[key],
            color: bgColor, // Use the same color as archetype title
            maxValue: 10
          });
        }
      });

      console.log(`Final metrics for ${name}:`, metrics);
      return metrics;
    }

    console.log(`Using fallback metrics for ${name} - no personalityMetrics found`);
    console.log(`apiPersonStats exists: ${!!apiPersonStats}`);
    console.log(`personalityMetrics exists: ${!!apiPersonStats?.personalityMetrics}`);

    // Fallback to mock data with archetype color if no API data
    return [
      { name: "Media Lover", value: 7, color: bgColor, maxValue: 10 },
      { name: "Spammer", value: 4, color: bgColor, maxValue: 10 },
      { name: "Conversation Starter", value: 9, color: bgColor, maxValue: 10 },
      { name: "Quick Responder", value: 8, color: bgColor, maxValue: 10 }
    ];
  }, [apiPersonStats, name, bgColor]);

  // Mock data for common phrases if not provided by API
  const mockCommonPhrases = [
    {
      phrase: "I can't even",
      count: 37,
      examples: [
        "I can't even with this group chat right now 😩",
        "I can't even believe you just said that!"
      ]
    },
    {
      phrase: "That's so fire",
      count: 32,
      examples: [
        "That's so fire! Did you see the new trailer?",
        "Your outfit is so fire today 🔥"
      ]
    },
    {
      phrase: "No way",
      count: 28,
      examples: [
        "No way! Are you serious?",
        "No way that just happened 😱"
      ]
    }
  ];

  return (
    <Card
      className={`overflow-hidden transition-all duration-300 ${isExpanded ? 'z-50 shadow-lg scale-[1.02]' : 'shadow-md hover:shadow-lg'}`}
      style={{
        marginTop: index > 0 ? '-12px' : '0',
        position: 'relative',
        zIndex: isExpanded ? 50 : 10 - index, // Higher z-index when expanded
        borderRadius: '16px',
        transform: isExpanded ? 'translateY(-2px)' : 'none'
      }}
      data-character-card={name}
    >
      {/* Card Header - Always visible */}
      <div
        className="py-3 px-3 sm:px-4 flex justify-between items-center cursor-pointer relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg, ${bgColor} 0%, ${bgColor}99 100%)`,
          boxShadow: 'inset 0 -1px 0 rgba(0,0,0,0.05)'
        }}        onClick={handleToggleExpand}
      >
        {/* Decorative elements - smaller on mobile */}
        <div className="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 rounded-full bg-white/10 -translate-y-8 sm:-translate-y-12 translate-x-4 sm:translate-x-8"></div>
        <div className="absolute bottom-0 left-0 w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-black/5 translate-y-6 sm:translate-y-8 -translate-x-6 sm:-translate-x-8"></div>        <div className="flex items-center relative z-10 min-w-0 flex-1 mr-2">
          <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white/30 backdrop-blur-sm mr-2 sm:mr-3 shadow-sm flex-shrink-0">
            <span className="text-lg sm:text-2xl">{emoji}</span>
          </div>          <div className="min-w-0 flex-1">
            <div className="text-sm sm:text-base font-medium">
              <span className="truncate">{name}</span>
            </div>
          </div>
        </div>        {/* Minimalist arrow indicator */}
        <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-white/20 backdrop-blur-sm relative z-10 flex-shrink-0 group-hover:bg-white/30 transition-all duration-200">
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 sm:h-5 sm:w-5 text-gray-700 transition-transform duration-200 group-hover:scale-110" />
          ) : (
            <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-gray-700 transition-transform duration-200 group-hover:scale-110" />
          )}
        </div>
      </div>

      {/* Description - Always visible in collapsed view */}
      <div className="px-3 sm:px-4 pt-3 pb-4 bg-white">
        <div className="font-bold text-sm sm:text-base mb-2">{archetype}</div>
        <p className="text-sm text-gray-600 mb-3 leading-relaxed">{personStats.description || description}</p>

        {/* Award Badges - Always show section */}
        <div className="mt-3">

          {(() => {
            console.log(`Awards debug for ${name}:`, {
              personStatsAwards: personStats.awards,
              awardsLength: personStats.awards?.length,
              awardsExists: !!personStats.awards,
              apiPersonStatsAwards: apiPersonStats?.awards,
              mockDataAwards: mockData.awards,
              rawApiPersonStats: apiPersonStats
            });

            // FORCE USE API AWARDS if apiPersonStats exists
            let awardsToShow = [];

            if (apiPersonStats?.awards && Array.isArray(apiPersonStats.awards) && apiPersonStats.awards.length > 0) {
              awardsToShow = apiPersonStats.awards;
              console.log(`Using API awards for ${name}:`, awardsToShow);
            } else if (personStats.awards && personStats.awards.length > 0) {
              awardsToShow = personStats.awards;
              console.log(`Using personStats awards for ${name}:`, awardsToShow);
            } else if (mockData.awards && mockData.awards.length > 0) {
              awardsToShow = mockData.awards;
              console.log(`Using mock awards for ${name}:`, awardsToShow);
            }

            console.log(`Final awards to show for ${name}:`, awardsToShow);

            return awardsToShow;
          })().length > 0 ? (
            <div className="flex flex-wrap gap-1.5 sm:gap-2">
              {(() => {
                // Use the same logic as above to get awards
                let awardsToShow = [];

                if (apiPersonStats?.awards && Array.isArray(apiPersonStats.awards) && apiPersonStats.awards.length > 0) {
                  awardsToShow = apiPersonStats.awards;
                } else if (personStats.awards && personStats.awards.length > 0) {
                  awardsToShow = personStats.awards;
                } else if (mockData.awards && mockData.awards.length > 0) {
                  awardsToShow = mockData.awards;
                }

                return awardsToShow.map((award: any, index: number) => {
                  // Determine tooltip positioning based on index to prevent off-screen issues
                  const isFirstAward = index === 0;
                  const isSecondAward = index === 1;
                  const isThirdAward = index === 2;
                  const isLastAward = index === awardsToShow.length - 1;                  // All tooltips centered and same width as badge
                  const tooltipPositionClasses = "left-1/2 -translate-x-1/2";
                  const arrowPositionClasses = "left-1/2 -translate-x-1/2";

                  return (
                    <div
                      key={index}
                      className="group relative flex items-center gap-1 sm:gap-1.5 px-1.5 sm:px-2 py-1 rounded-full border shadow-sm hover:shadow-md transition-all duration-200"
                      style={{ borderColor: `${award.color}40`, background: `${award.color}10` }}
                    >
                      <div
                        className="flex items-center justify-center w-4 h-4 sm:w-5 sm:h-5 rounded-full shadow-sm flex-shrink-0"
                        style={{ background: `${award.color}30` }}
                      >
                        <span className="text-xs sm:text-sm">{award.icon}</span>
                      </div>
                      <span className="text-xs font-medium text-gray-700 whitespace-nowrap">{award.title}</span>                      {/* Tooltip - centered and same width as badge */}
                      <div className={`absolute bottom-full ${tooltipPositionClasses} mb-2 w-full p-2 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-[100] pointer-events-none`}>
                        <div className="text-xs font-medium mb-1 flex items-center gap-1">
                          <span>{award.icon}</span>
                          <span>{award.title}</span>
                        </div>
                        <div className="text-xs text-gray-600">{award.description}</div>
                        {/* Arrow pointing down */}
                        <div className={`absolute -bottom-1 ${arrowPositionClasses} w-2 h-2 bg-white border-b border-r border-gray-200 transform rotate-45`}></div>
                      </div>
                    </div>
                  );
                });
              })()}
            </div>
          ) : (
            <div className="text-xs text-gray-500 italic">
              No awards available - Debug: {JSON.stringify({
                hasApiPersonStats: !!apiPersonStats,
                hasApiAwards: !!apiPersonStats?.awards,
                apiAwardsLength: apiPersonStats?.awards?.length,
                hasPersonStatsAwards: !!personStats.awards,
                personStatsAwardsLength: personStats.awards?.length
              })}
            </div>
          )}
        </div>
      </div>

      {/* Expandable Content */}
      {(isExpanded || animationState === 'collapsing') && (
        <div className={`expandable-content p-3 sm:p-4 pt-0 border-t bg-white/90 backdrop-blur-sm overflow-hidden ${
          animationState === 'expanding' ? 'slide-up-from-behind' :
          animationState === 'collapsing' ? 'slide-down-behind' :
          'opacity-100'
        }`}>

          {/* Power Move & Weakness section removed - shown in component below */}

          {/* Tabs for Personality and Funny Metrics */}
          <Tabs value={activeTab} onValueChange={(value) => {
            hapticFeedback.selection();
            setActiveTab(value);
          }}>
            <TabsList className="grid grid-cols-2 w-full mb-3">
              <TabsTrigger value="personality" className="flex items-center gap-1 text-xs sm:text-sm">
                <User className="h-3 w-3" />
                <span className="hidden sm:inline">Personality</span>
                <span className="sm:hidden">Traits</span>
              </TabsTrigger>
              <TabsTrigger value="funny-metrics" className="flex items-center gap-1 text-xs sm:text-sm">
                <BarChart2 className="h-3 w-3" />
                <span className="hidden sm:inline">Funny Metrics</span>
                <span className="sm:hidden">Metrics</span>
              </TabsTrigger>

            </TabsList>

            {/* Personality Tab Content */}
            <TabsContent value="personality" className="mt-0">
              <div className="space-y-2">
                <div className="space-y-2">
                  {personStats.traits && personStats.traits.length > 0 ? (
                    // If we have traits, display them
                    personStats.traits.map((trait: any, index: number) => (
                      <div key={index} className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span>{trait.name}</span>
                          <span>{trait.value}%</span>
                        </div>                        <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                          <div
                            className="h-full rounded-full transition-all"
                            style={{
                              width: `${trait.value}%`,
                              background: `linear-gradient(to right, ${bgColor}80, ${bgColor})`
                            }}
                          />
                        </div>
                      </div>
                    ))
                  ) : (
                    // If no traits are available, show a message
                    <div className="text-xs text-gray-500 italic">
                      No personality traits data available
                    </div>
                  )}
                </div>
                <div className="text-xs text-gray-500 italic mt-1">
                  {personStats.mood}
                </div>
              </div>
            </TabsContent>

            {/* Funny Metrics Tab Content */}
            <TabsContent value="funny-metrics" className="mt-0">
              <FunnyMetricsChart metrics={funnyMetrics} />
            </TabsContent>
          </Tabs>

          {/* Favorite Topics - Only show if available */}
          {personStats.favoriteTopics && personStats.favoriteTopics.length > 0 && (
            <div className="mt-4 space-y-2">
              <div className="text-xs font-medium text-gray-500 flex items-center gap-1">
                <span className="text-sm">🔍</span>
                <span>Favorite Topics</span>
              </div>
              <div className="flex flex-wrap gap-1.5 sm:gap-2">
                {personStats.favoriteTopics.map((topic: string, index: number) => (
                  <div
                    key={index}
                    className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 whitespace-nowrap"
                  >
                    {topic}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Communication Stats section removed as requested */}

          {/* Memorable Moments - Only show if available */}
          {(() => {
            // Check for funnyQuotes from API data or commonPhrases
            const quotes = apiPersonStats?.funnyQuotes || personStats.commonPhrases || [];
            const hasQuotes = quotes.length > 0;
            const hasMockData = mockCommonPhrases.length > 0;

            console.log(`Memorable moments for ${name}:`, {
              funnyQuotes: apiPersonStats?.funnyQuotes,
              commonPhrases: personStats.commonPhrases,
              memorable_moments: apiPersonStats?.memorable_moments,
              inside_jokes: apiPersonStats?.inside_jokes,
              quotes: quotes,
              hasQuotes: hasQuotes,
              quotesLength: quotes.length
            });

            return hasQuotes || hasMockData;
          })() && (
            <div className="mt-4 space-y-2">
              <div className="text-xs font-medium text-gray-500 flex items-center gap-1">
                <span className="text-sm">💬</span>
                <span>Memorable Moments</span>
              </div>
              <div className="flex flex-wrap gap-1.5 sm:gap-2">
                {(() => {
                  // Use funnyQuotes from API if available, otherwise use commonPhrases or mock data
                  const quotes = apiPersonStats?.funnyQuotes || personStats.commonPhrases || mockCommonPhrases;

                  return quotes.slice(0, 2).map((item: any, index: number) => {
                    // Handle both string format (funnyQuotes) and object format (commonPhrases)
                    const displayText = typeof item === 'string' ? item : (item.phrase || item);

                    return (
                      <div
                        key={index}
                        className="px-2 sm:px-3 py-1.5 rounded-full bg-blue-50/30 border border-blue-200/40 text-xs font-medium text-blue-800"
                      >
                        "{displayText}"
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default CharacterCard;
