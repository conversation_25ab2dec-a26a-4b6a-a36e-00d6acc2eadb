import React, { createContext, useContext, ReactNode, useEffect, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

// Define the app states
export enum AnalysisState {
  UPLOAD,
  ANALYZING,
  TUTORIAL,
  DASHBOARD
}

// Define the context type
interface AppStateContextType {
  state: AnalysisState;
  setState: (state: AnalysisState) => void;
  isSubscriptionModalOpen: boolean;
  showSubscriptionModal: (action?: () => void) => void;
  hideSubscriptionModal: () => void;
  hideSubscriptionModalAfterSuccess: () => void;
  cancelSubscriptionFlow: () => void;
  postPurchaseAction: (() => void) | null;
  isDailyLimitDialogOpen: boolean;
  showDailyLimitDialog: () => void;
  hideDailyLimitDialog: () => void;
  previousState: AnalysisState | null;
  hasFileBeenProcessed: boolean;
  setFileProcessed: (processed: boolean) => void;
}

// Create the context with a default value
const AppStateContext = createContext<AppStateContextType | undefined>(undefined);

// Provider component
interface AppStateProviderProps {
  children: ReactNode;
}

export const AppStateProvider: React.FC<AppStateProviderProps> = ({ children }) => {
  const location = useLocation();
  const [isSubscriptionModalOpen, setSubscriptionModalOpen] = useState(false);
  const [postPurchaseAction, setPostPurchaseAction] = useState<(() => void) | null>(null);
  const [isDailyLimitDialogOpen, setDailyLimitDialogOpen] = useState(false);
  const [previousState, setPreviousState] = useState<AnalysisState | null>(null);
  const [hasFileBeenProcessed, setHasFileBeenProcessed] = useState(false);


  // Initialize state based on URL search params
  const getInitialState = (): AnalysisState => {
    if (location.search.includes('state=DASHBOARD')) {
      return AnalysisState.DASHBOARD;
    } else if (location.search.includes('state=TUTORIAL')) {
      return AnalysisState.TUTORIAL;
    } else if (location.search.includes('state=ANALYZING')) {
      return AnalysisState.ANALYZING;
    } else {
      return AnalysisState.UPLOAD;
    }
  };

  const [state, setStateInternal] = React.useState<AnalysisState>(getInitialState());

  // Custom setState that tracks previous state
  const setState = useCallback((newState: AnalysisState) => {
    setStateInternal(currentState => {
      // Only track previous state when transitioning to ANALYZING
      if (newState === AnalysisState.ANALYZING && currentState !== AnalysisState.ANALYZING) {
        setPreviousState(currentState);
      }
      return newState;
    });
  }, []);

  const showSubscriptionModal = useCallback((action?: () => void) => {
    if (action) {
      setPostPurchaseAction(() => action);
    }
    setSubscriptionModalOpen(true);
  }, []);

  const hideSubscriptionModal = useCallback(() => {
    setSubscriptionModalOpen(false);
    setPostPurchaseAction(null);
    // When subscription modal is closed (including X button), return to homepage
    // This ensures consistent behavior for subscription failures
    const targetState = previousState || AnalysisState.UPLOAD;
    setState(targetState);
    setPreviousState(null); // Clear previous state after using it
  }, [previousState, setState]);

  const cancelSubscriptionFlow = useCallback(() => {
    setSubscriptionModalOpen(false);
    setPostPurchaseAction(null);
    // User explicitly cancelled subscription, always return to upload state
    // This ensures that when subscription fails during manual upload, user returns to homepage
    const targetState = previousState || AnalysisState.UPLOAD;
    setState(targetState);
    setPreviousState(null); // Clear previous state after using it
  }, [previousState, setState]);

  const showDailyLimitDialog = useCallback(() => {
    setDailyLimitDialogOpen(true);
  }, []);

  const hideDailyLimitDialog = useCallback(() => {
    setDailyLimitDialogOpen(false);
    // If we were in analyzing state and user closes daily limit dialog, go back to upload
    if (state === AnalysisState.ANALYZING) {
      setState(AnalysisState.UPLOAD);
    }
  }, [state]);

  const setFileProcessed = useCallback((processed: boolean) => {
    setHasFileBeenProcessed(processed);
  }, []);

  const hideSubscriptionModalAfterSuccess = useCallback(() => {
    setSubscriptionModalOpen(false);
    setPostPurchaseAction(null);
    // Don't navigate anywhere - let the postPurchaseAction handle navigation
    setPreviousState(null); // Clear previous state
  }, []);


  // Update state when URL changes - but only on initial load
  // We'll use a ref to track if this is the initial load
  const initialLoadRef = React.useRef(true);

  useEffect(() => {
    // Only update state from URL on initial load
    if (initialLoadRef.current) {
      initialLoadRef.current = false;
      const newState = getInitialState();
      if (newState !== state) {
        setState(newState);
      }
    }
  }, []);

  return (
    <AppStateContext.Provider value={{
      state,
      setState,
      isSubscriptionModalOpen,
      showSubscriptionModal,
      hideSubscriptionModal,
      hideSubscriptionModalAfterSuccess,
      cancelSubscriptionFlow,
      postPurchaseAction,
      isDailyLimitDialogOpen,
      showDailyLimitDialog,
      hideDailyLimitDialog,
      previousState,
      hasFileBeenProcessed,
      setFileProcessed
    }}>
      {children}
    </AppStateContext.Provider>
  );
};

// Custom hook to use the app state context
export const useAppState = (): AppStateContextType => {
  const context = useContext(AppStateContext);
  if (context === undefined) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};
