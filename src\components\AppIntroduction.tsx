import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowRight, Sparkles, MessageSquare, Users, BarChart3 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { hapticFeedback } from '@/utils/haptics';
import CustomSplashScreen from './CustomSplashScreen';

interface IntroPage {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient: string;
}

const introPages: IntroPage[] = [
  {
    id: 1,
    title: 'Welcome to Chat<PERSON>uster',
    description: 'Discover the hidden patterns, personalities, and dynamics in your chat conversations with AI-powered analysis.',
    icon: <Sparkles className="w-16 h-16 text-white" />,
    gradient: 'from-purple-600 to-blue-600'
  },
  {
    id: 2,
    title: 'Analyze Group Dynamics',
    description: 'Uncover who talks the most, who gets the most reactions, and discover the unique personality of each group member.',
    icon: <Users className="w-16 h-16 text-white" />,
    gradient: 'from-blue-600 to-indigo-600'
  },
  {
    id: 3,
    title: 'Get Detailed Insights',
    description: 'From conversation starters to emoji usage patterns, get comprehensive insights about your chat relationships and communication styles.',
    icon: <BarChart3 className="w-16 h-16 text-white" />,
    gradient: 'from-indigo-600 to-purple-600'
  }
];

const AppIntroduction: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(0);
  const [showSplash, setShowSplash] = useState(true);
  const [logoAnimated, setLogoAnimated] = useState(false);

  // Handle splash screen and logo animation
  useEffect(() => {
    const splashTimer = setTimeout(() => {
      setShowSplash(false);
      // Start logo animation after splash
      setTimeout(() => {
        setLogoAnimated(true);
      }, 500);
    }, 3000);

    return () => clearTimeout(splashTimer);
  }, []);

  const nextPage = () => {
    if (currentPage < introPages.length - 1) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage - 1);
    }
  };

  const handleFinish = () => {
    hapticFeedback.buttonPress();
    navigate('/', { replace: true });
  };

  // Use CustomSplashScreen
  if (showSplash) {
    return <CustomSplashScreen />;
  }

  const currentPageData = introPages[currentPage];
  const isLastPage = currentPage === introPages.length - 1;

  return (
    <div className="min-h-screen relative overflow-hidden" style={{ backgroundColor: '#f0bbcc' }}>
      {/* Header with page indicators only */}
      <div className="absolute top-0 left-0 right-0 z-10 p-4 flex justify-center items-center">
        {/* Page indicators */}
        <div className="flex space-x-2">
          {introPages.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentPage ? 'bg-white w-6' : 'bg-white/50'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Animated Logo Section */}
      <motion.div
        initial={{ y: 0 }}
        animate={{ y: logoAnimated ? -200 : 0 }}
        transition={{ duration: 1.0, ease: "easeInOut" }}
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center z-20"
        style={{ pointerEvents: logoAnimated ? 'none' : 'auto' }}
      >
        <motion.div
          initial={{ scale: 1 }}
          animate={{ scale: logoAnimated ? 0.4 : 1 }}
          transition={{ duration: 1.0, ease: "easeInOut" }}
          className="flex flex-col items-center"
        >
          <img
            src="/icons/header_logo.webp"
            alt="Chatbuster Logo"
            className="w-48 h-24 object-contain mb-4"
          />
        </motion.div>
      </motion.div>

      {/* Main Content */}
      <AnimatePresence mode="wait">
        {logoAnimated && (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col items-center justify-center min-h-screen px-6 pt-32 pb-24"
          >
            {/* Welcome Text */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-2xl font-semibold text-white mb-4">
                Welcome to ChatBuster
              </h2>
            </motion.div>

            {/* Page Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPage}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.4 }}
                className="text-center max-w-md mx-auto"
              >
                {/* Icon */}
                <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-r ${currentPageData.gradient} rounded-full flex items-center justify-center`}>
                  {currentPageData.icon}
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-white mb-4">
                  {currentPageData.title}
                </h3>

                {/* Description */}
                <p className="text-white/90 text-lg leading-relaxed">
                  {currentPageData.description}
                </p>
              </motion.div>
            </AnimatePresence>

            {/* Continue Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="mt-12 w-full max-w-md"
            >
              <Button
                onClick={isLastPage ? handleFinish : nextPage}
                className="w-full h-12 bg-white/10 hover:bg-white/20 text-white border border-white/30 hover:border-white/50 text-lg font-semibold transition-all duration-300"
              >
                {isLastPage ? (
                  <>
                    Get Started
                    <Sparkles className="w-5 h-5 ml-2" />
                  </>
                ) : (
                  <>
                    Continue
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </>
                )}
              </Button>

              {/* Previous button for non-first pages */}
              {currentPage > 0 && (
                <Button
                  variant="ghost"
                  onClick={prevPage}
                  className="w-full mt-3 text-white/80 hover:text-white hover:bg-white/10"
                >
                  Back
                </Button>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AppIntroduction;
