import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowRight, Sparkles, MessageSquare, Users, BarChart3 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { hapticFeedback } from '@/utils/haptics';
import CustomSplashScreen from './CustomSplashScreen';

interface IntroPage {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient: string;
}

const introPages: IntroPage[] = [
  {
    id: 1,
    title: 'Welcome to ChatBuster',
    description: '',
    icon: <Sparkles className="w-16 h-16 text-white" />,
    gradient: 'from-purple-600 to-blue-600'
  },
  {
    id: 2,
    title: 'Discover Hidden Patterns',
    description: 'Discover the hidden patterns, personalities, and dynamics in your chat conversations with AI-powered analysis.',
    icon: <MessageSquare className="w-16 h-16 text-white" />,
    gradient: 'from-purple-600 to-blue-600'
  },
  {
    id: 3,
    title: 'Analyze Group Dynamics',
    description: 'Uncover who talks the most, who gets the most reactions, and discover the unique personality of each group member.',
    icon: <Users className="w-16 h-16 text-white" />,
    gradient: 'from-blue-600 to-indigo-600'
  },
  {
    id: 4,
    title: 'Get Detailed Insights',
    description: 'From conversation starters to emoji usage patterns, get comprehensive insights about your chat relationships and communication styles.',
    icon: <BarChart3 className="w-16 h-16 text-white" />,
    gradient: 'from-indigo-600 to-purple-600'
  }
];

const AppIntroduction: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(0);
  const [showSplash, setShowSplash] = useState(true);
  const [logoAnimated, setLogoAnimated] = useState(false);

  // Handle splash screen and logo animation
  useEffect(() => {
    const splashTimer = setTimeout(() => {
      setShowSplash(false);
      // Start logo animation after splash
      setTimeout(() => {
        setLogoAnimated(true);
      }, 500);
    }, 3000);

    return () => clearTimeout(splashTimer);
  }, []);

  const nextPage = () => {
    if (currentPage < introPages.length - 1) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage - 1);
    }
  };

  const handleFinish = () => {
    hapticFeedback.buttonPress();
    navigate('/', { replace: true });
  };

  // Use CustomSplashScreen
  if (showSplash) {
    return <CustomSplashScreen />;
  }

  const currentPageData = introPages[currentPage];
  const isLastPage = currentPage === introPages.length - 1;

  return (
    <div className="min-h-screen relative overflow-hidden" style={{ backgroundColor: '#f0bbcc' }}>

      {/* Animated Logo Section */}
      <motion.div
        initial={{
          position: "absolute",
          top: "50%",
          left: "50%",
          x: "-50%",
          y: "-50%"
        }}
        animate={{
          position: logoAnimated ? "absolute" : "absolute",
          top: logoAnimated ? "80px" : "50%",
          left: "50%",
          x: "-50%",
          y: logoAnimated ? "0%" : "-50%"
        }}
        transition={{ duration: 1.2, ease: "easeInOut" }}
        className="z-20"
        style={{ pointerEvents: logoAnimated ? 'none' : 'auto' }}
      >
        <motion.div
          initial={{ scale: 1 }}
          animate={{ scale: logoAnimated ? 1.2 : 1 }}
          transition={{ duration: 1.2, ease: "easeInOut" }}
          className="relative flex flex-col items-center"
        >
          <img
            src="/icons/header_logo.webp"
            alt="Chatbuster Logo"
            className="w-48 h-24 object-contain mb-4"
          />
          
          {/* Invisible placeholder for loading circle space */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 0 }}
            className="relative w-16 h-16"
          />
        </motion.div>
      </motion.div>

      {/* Main Content */}
      <AnimatePresence mode="wait">
        {logoAnimated && (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col min-h-screen px-6 pt-40"
          >
            {/* Page Content - Flexible space */}
            <div className="flex-1 flex items-center justify-center">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentPage}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.4 }}
                  className="text-center max-w-md mx-auto"
                >
                  {currentPage === 0 ? (
                    /* Welcome page - title only */
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5, duration: 0.6 }}
                      className="text-center"
                    >
                      <h2 className="text-4xl font-bold text-white drop-shadow-2xl" style={{ textShadow: '3px 3px 6px rgba(0,0,0,0.8), 0 0 20px rgba(0,0,0,0.5)' }}>
                        Welcome to ChatBuster
                      </h2>
                    </motion.div>
                  ) : (
                    /* Info pages */
                    <>
                      {/* Icon */}
                      <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-r ${currentPageData.gradient} rounded-full flex items-center justify-center shadow-2xl`}>
                        {currentPageData.icon}
                      </div>

                      {/* Content with backdrop */}
                      <div className="bg-black/20 backdrop-blur-sm rounded-3xl p-6 border border-white/20 shadow-2xl">
                        {/* Title */}
                        <h3 className="text-2xl font-bold text-white mb-4 drop-shadow-lg" style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}>
                          {currentPageData.title}
                        </h3>

                        {/* Description */}
                        <p className="text-white text-lg leading-relaxed drop-shadow-md" style={{ textShadow: '1px 1px 3px rgba(0,0,0,0.7)' }}>
                          {currentPageData.description}
                        </p>
                      </div>
                    </>
                  )}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Fixed Bottom Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: currentPage === 0 ? 1.2 : 0.8, duration: 0.6 }}
              className="pb-8 w-full max-w-md mx-auto"
            >
              <Button
                onClick={isLastPage ? handleFinish : nextPage}
                className="w-full h-12 bg-white/10 hover:bg-white/20 text-white border border-white/30 hover:border-white/50 text-lg font-semibold transition-all duration-300"
              >
                {isLastPage ? (
                  <>
                    Get Started
                    <Sparkles className="w-5 h-5 ml-2" />
                  </>
                ) : (
                  <>
                    Continue
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </>
                )}
              </Button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AppIntroduction;
