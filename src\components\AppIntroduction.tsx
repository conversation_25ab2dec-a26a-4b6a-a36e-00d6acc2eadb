import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight, Sparkles, MessageSquare, Users, BarChart3 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { hapticFeedback } from '@/utils/haptics';

interface IntroPage {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient: string;
}

const introPages: IntroPage[] = [
  {
    id: 1,
    title: 'Welcome to ChatBuster',
    description: 'Discover the hidden patterns, personalities, and dynamics in your chat conversations with AI-powered analysis.',
    icon: <Sparkles className="w-16 h-16 text-white" />,
    gradient: 'from-purple-600 to-blue-600'
  },
  {
    id: 2,
    title: 'Analyze Group Dynamics',
    description: 'Uncover who talks the most, who gets the most reactions, and discover the unique personality of each group member.',
    icon: <Users className="w-16 h-16 text-white" />,
    gradient: 'from-blue-600 to-indigo-600'
  },
  {
    id: 3,
    title: 'Get Detailed Insights',
    description: 'From conversation starters to emoji usage patterns, get comprehensive insights about your chat relationships and communication styles.',
    icon: <BarChart3 className="w-16 h-16 text-white" />,
    gradient: 'from-indigo-600 to-purple-600'
  }
];

const AppIntroduction: React.FC = () => {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(0);
  const [showSplash, setShowSplash] = useState(true);
  const [logoAnimated, setLogoAnimated] = useState(false);

  // Handle splash screen and logo animation
  useEffect(() => {
    const splashTimer = setTimeout(() => {
      setShowSplash(false);
      // Start logo animation after splash
      setTimeout(() => {
        setLogoAnimated(true);
      }, 300);
    }, 2000);

    return () => clearTimeout(splashTimer);
  }, []);

  const nextPage = () => {
    if (currentPage < introPages.length - 1) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      hapticFeedback.buttonPress();
      setCurrentPage(currentPage - 1);
    }
  };

  const handleFinish = () => {
    hapticFeedback.buttonPress();
    navigate('/', { replace: true });
  };

  const handleBack = () => {
    hapticFeedback.buttonPress();
    navigate('/', { replace: true });
  };

  // Splash Screen
  if (showSplash) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-600 to-blue-600 flex items-center justify-center">
        <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center"
        >
          <div className="w-24 h-24 mx-auto mb-6 bg-white rounded-full flex items-center justify-center">
            <Sparkles className="w-12 h-12 text-purple-600" />
          </div>
          <motion.h1
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="text-4xl font-bold text-white"
          >
            ChatBuster
          </motion.h1>
        </motion.div>
      </div>
    );
  }

  const currentPageData = introPages[currentPage];
  const isLastPage = currentPage === introPages.length - 1;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50 relative overflow-hidden">
      {/* Header with back button */}
      <div className="absolute top-0 left-0 right-0 z-10 p-4 flex justify-between items-center">
        <Button
          variant="ghost"
          size="icon"
          className="h-12 w-12 rounded-full hover:bg-white/20 focus:ring-2 focus:ring-purple-400"
          onClick={handleBack}
        >
          <ArrowLeft className="h-6 w-6 text-purple-700" />
          <span className="sr-only">Back to Home</span>
        </Button>
        
        {/* Page indicators */}
        <div className="flex space-x-2">
          {introPages.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentPage ? 'bg-purple-600 w-6' : 'bg-purple-300'
              }`}
            />
          ))}
        </div>
      </div>

      {/* Animated Logo Section */}
      <motion.div
        initial={{ y: 0 }}
        animate={{ y: logoAnimated ? -100 : 0 }}
        transition={{ duration: 0.8, ease: "easeInOut" }}
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center z-20"
        style={{ pointerEvents: logoAnimated ? 'none' : 'auto' }}
      >
        <motion.div
          initial={{ scale: 1 }}
          animate={{ scale: logoAnimated ? 0.6 : 1 }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
          className="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center"
        >
          <Sparkles className="w-10 h-10 text-white" />
        </motion.div>
        <motion.h1
          initial={{ fontSize: '2rem' }}
          animate={{ fontSize: logoAnimated ? '1.5rem' : '2rem' }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
          className="font-bold text-purple-700"
        >
          ChatBuster
        </motion.h1>
      </motion.div>

      {/* Main Content */}
      <AnimatePresence mode="wait">
        {logoAnimated && (
          <motion.div
            key="content"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col items-center justify-center min-h-screen px-6 pt-32 pb-24"
          >
            {/* Welcome Text */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="text-center mb-12"
            >
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">
                Welcome to ChatBuster
              </h2>
            </motion.div>

            {/* Page Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentPage}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.4 }}
                className="text-center max-w-md mx-auto"
              >
                {/* Icon */}
                <div className={`w-20 h-20 mx-auto mb-6 bg-gradient-to-r ${currentPageData.gradient} rounded-full flex items-center justify-center`}>
                  {currentPageData.icon}
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-gray-800 mb-4">
                  {currentPageData.title}
                </h3>

                {/* Description */}
                <p className="text-gray-600 text-lg leading-relaxed">
                  {currentPageData.description}
                </p>
              </motion.div>
            </AnimatePresence>

            {/* Continue Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="mt-12 w-full max-w-md"
            >
              <Button
                onClick={isLastPage ? handleFinish : nextPage}
                className={`w-full h-12 bg-gradient-to-r ${currentPageData.gradient} hover:opacity-90 text-white border-0 text-lg font-semibold`}
              >
                {isLastPage ? (
                  <>
                    Get Started
                    <Sparkles className="w-5 h-5 ml-2" />
                  </>
                ) : (
                  <>
                    Continue
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </>
                )}
              </Button>

              {/* Previous button for non-first pages */}
              {currentPage > 0 && (
                <Button
                  variant="ghost"
                  onClick={prevPage}
                  className="w-full mt-3 text-purple-600 hover:text-purple-700"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AppIntroduction;
