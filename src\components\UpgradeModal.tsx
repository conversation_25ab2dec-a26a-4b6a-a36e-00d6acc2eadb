import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, Crown, Sparkles } from 'lucide-react';
import { getLocalizedPricing } from '@/services/pricing';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpgrade?: () => void;
  currentTier?: 'free' | 'premium';
}

export const UpgradeModal: React.FC<UpgradeModalProps> = ({
  isOpen,
  onClose,
  onUpgrade,
  currentTier = 'free'
}) => {
  const [localizedPrice, setLocalizedPrice] = useState('€4.99');

  useEffect(() => {
    const fetchPrice = async () => {
      const pricing = await getLocalizedPricing();
      setLocalizedPrice(pricing.price);
    };
    fetchPrice();
  }, []);
  const handleUpgrade = () => {
    if (onUpgrade) {
      onUpgrade();
    } else {
      // Placeholder for actual upgrade logic
      console.log('Upgrade to premium initiated');
      // TODO: Integrate with RevenueCat when implemented
    }
    onClose();
  };

  const features = [
    '30 analyses per month',
    'All analysis types',
    'Priority processing',
    'Advanced insights',
    'No ads'
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Crown className="w-5 h-5 text-yellow-500" />
            <span>Upgrade to Premium</span>
          </DialogTitle>
          <DialogDescription>
            Unlock unlimited chat analysis and premium features
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current vs Premium comparison */}
          <div className="grid grid-cols-2 gap-3">
            {/* Free tier */}
            <Card className="border-gray-200">
              <CardContent className="p-4 space-y-3">
                <div className="flex items-center space-x-2">
                  <Sparkles className="w-4 h-4 text-gray-500" />
                  <Badge variant="secondary">Free</Badge>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">1</div>
                  <div className="text-sm text-gray-500">analysis/month</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-600">✓ Basic analysis</div>
                  <div className="text-xs text-gray-400">✗ Limited features</div>
                </div>
              </CardContent>
            </Card>

            {/* Premium tier */}
            <Card className="border-yellow-300 bg-yellow-50">
              <CardContent className="p-4 space-y-3">
                <div className="flex items-center space-x-2">
                  <Crown className="w-4 h-4 text-yellow-500" />
                  <Badge className="bg-yellow-500">Premium</Badge>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">30</div>
                  <div className="text-sm text-yellow-600">analyses/month</div>
                </div>
                <div className="space-y-1">
                  {features.slice(0, 2).map((feature, index) => (
                    <div key={index} className="text-xs text-yellow-700 flex items-center space-x-1">
                      <Check className="w-3 h-3" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Premium features */}
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Premium includes:</h4>
            <div className="grid grid-cols-1 gap-2">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-gray-600">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Pricing */}
          <div className="text-center p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-gray-800">{localizedPrice}</div>
            <div className="text-sm text-gray-600">per week</div>
            <div className="text-xs text-gray-500 mt-1">Cancel anytime</div>
          </div>

          {/* Action buttons */}
          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Maybe Later
            </Button>
            <Button onClick={handleUpgrade} className="flex-1 bg-yellow-500 hover:bg-yellow-600">
              Upgrade Now
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="text-center">
            <div className="text-xs text-gray-500 space-y-1">
              <div>✓ Secure payment via App Store/Google Play</div>
              <div>✓ Cancel anytime</div>
              <div>✓ Instant activation</div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UpgradeModal;