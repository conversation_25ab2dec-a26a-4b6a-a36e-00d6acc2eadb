import type { CapacitorConfig } from '@capacitor/cli';
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Get LOCAL_IP_ADDRESS from process.env
const LOCAL_IP_ADDRESS = process.env.LOCAL_IP_ADDRESS || 'http://localhost:3000';

// Production mode check
const isProductionMode = process.env.VITE_PRODUCTION_MODE === 'true';

// Determine server URL based on production mode
const getServerUrl = () => {
  if (isProductionMode) {
    return 'https://chatbuster.vibalyze.ee';
  }
  return LOCAL_IP_ADDRESS;
};

const serverUrl = getServerUrl();

console.log('Capacitor Configuration:', {
  isProductionMode: isProductionMode,
  serverUrl: serverUrl
});

const config: CapacitorConfig = {
  appId: 'app.chatvibeanalyzer.mobile',
  appName: 'Chatbuster',
  webDir: 'dist',
  server: {
    // Use production URL in production mode, localhost for development
    url: serverUrl,
    cleartext: !isProductionMode, // Only allow cleartext in development
    androidScheme: 'https'
  },
  // Enable live reload
  plugins: {
    SplashScreen: {
      launchShowDuration: 2000,
      launchAutoHide: false,
      launchFadeOutDuration: 300,
      backgroundColor: "#f0bbcc",
      androidSplashResourceName: "splash",
      androidScaleType: "FIT_CENTER",
      showSpinner: false,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#999999",
      splashFullScreen: true,
      splashImmersive: true,
      layoutName: "launch_screen",
      useDialog: false,
    },
    LiveUpdates: {
      enabled: true,
      autoUpdateEnabled: true
    },
    ScreenOrientation: {
      // Lock the screen orientation to portrait
      lockOrientation: 'portrait'
    },
    // Add file opener configuration
    FileOpener: {
      // File associations
      associations: [
        {
          ext: 'txt',
          mimeType: 'text/plain'
        },
        {
          ext: 'zip',
          mimeType: 'application/zip'
        }
      ]
    },
    // Add app URL configuration for deep links
    App: {
      url: 'chatvibeanalyzer://',
      allowRedirect: true
    },
    // Add file system configuration
    Filesystem: {
      readChunkSize: 1024 * 1024 // 1MB chunks for better performance
    },
    // RevenueCat configuration for subscriptions
    PurchasesCapacitor: {
      // Android-specific configuration
      android: {
        // Enable Play Store integration
        enablePlayStoreIntegration: true,
        // Handle Play Store transactions
        enablePlayStoreObserverMode: false
      }
    }
  },
  // iOS specific configuration
  ios: {
    // Use 'automatic' to properly handle safe areas
    contentInset: 'never',
    // Handle URL schemes
    scheme: 'App',
    // Handle universal links
    handleApplicationNotifications: true
  }
};

export default config;
