import * as Sentry from "@sentry/react";
import { But<PERSON> } from "./ui/button";

export const SentryTestButton = () => {
  const testSentry = () => {
    try {
      Sentry.captureException(new Error("Test error from SentryTestButton"));
      Sentry.captureMessage("Test message from dev mode", "info");
      Sentry.addBreadcrumb({
        message: "User clicked Sentry test button",
        level: "info",
        category: "user-action"
      });
      
      alert("Sentry test events sent!");
    } catch (error) {
      alert("Failed to send Sentry events.");
    }
  };

  // Only show in development mode
  if (import.meta.env.PROD) {
    return null;
  }

  return (
    <Button 
      onClick={testSentry}
      variant="outline"
      className="fixed bottom-4 right-4 z-50 bg-red-500 text-white hover:bg-red-600"
    >
      Test Sentry
    </Button>
  );
};