/**
 * High-level purchase operations service
 * Provides simplified interface for subscription management using RevenueCat.
 */

import { toast } from '@/utils/toast';
import { Capacitor } from '@capacitor/core';
import {
  initializeRevenueCat, // We initialize this in App.tsx now
  getOfferings,
  purchasePackage as RCatPurchasePackage,
  restorePurchases as RCatRestorePurchases,
  isProActive as RCatIsProActive,
  getCustomerInfo as RCatGetCustomerInfo,
  findPackageByIdentifier,
  // ENTITLEMENT_ID_PRO, // Already defined in revenuecat.ts, or use a shared constant
} from './revenuecat'; // Updated import
import { PurchasesPackage, CustomerInfo, PurchasesOffering } from '@revenuecat/purchases-capacitor';

const PREMIUM_PRODUCT_ID = 'rc_0002'; // Your RevenueCat Product ID for the "Pro" subscription

/**
 * Initialize purchases system on app startup.
 * Note: RevenueCat SDK is now initialized in App.tsx with the appUserId.
 * This function can be used for any additional setup for this service if needed,
 * or to re-check/log status.
 */
export async function initializePurchases(): Promise<void> {
  try {
    console.log('Purchases service initializing (RevenueCat should be configured by App.tsx)...');
    // Optionally, perform an initial check or log status
    const isActive = await isPremiumUser();
    console.log('Initial premium status via purchases service:', isActive);
    // Ensure RevenueCat is configured (though App.tsx should handle primary init)
    // const appUserId = await getAppUserID(); // from device.ts or revenuecat.ts
    // if (appUserId) {
    //   await initializeRevenueCat(appUserId); // Redundant if App.tsx does it, but ensures it if called standalone
    // } else {
    //   console.warn("No App User ID available for RevenueCat initialization in purchases.ts");
    // }
    console.log('Purchases service ready.');
  } catch (error) {
    console.error('Failed to initialize purchases service:', error);
    // Do not throw here to prevent app crash if RC init in App.tsx already handled it
  }
}

/**
 * Handle restore purchases with user feedback using RevenueCat.
 */
export async function handleRestorePurchases(): Promise<boolean> {
  try {
    console.log('Attempting to restore purchases with RevenueCat...');
    toast.loading('Restoring purchases...', { id: 'restore-purchases' });

    const customerInfo: CustomerInfo | null = await RCatRestorePurchases();
    toast.dismiss('restore-purchases');

    if (customerInfo) {
      const isActive = customerInfo.entitlements.active['Pro'] !== undefined;
      if (isActive) {
        toast.success('Purchases restored successfully!');
        console.log('RevenueCat: Purchases restored, Pro entitlement active.');
        // Potentially navigate or update UI state
        // window.history.back(); // Consider if this is still the desired behavior
        return true;
      } else {
        toast.info('No active "Pro" subscription found to restore.');
        console.log('RevenueCat: Restore successful, but no active Pro entitlement found.');
        return false;
      }
    } else {
      // RCatRestorePurchases returning null usually means an error occurred (logged in revenuecat.ts)
      // or user cancelled (if the SDK presented a UI, though typically it doesn't for restore).
      toast.error('Failed to restore purchases. Please try again.');
      console.log('RevenueCat: Restore purchases returned null or failed.');
      return false;
    }
  } catch (error) {
    console.error('Failed to restore purchases (RevenueCat):', error);
    toast.dismiss('restore-purchases');
    toast.error('Failed to restore purchases. Please try again.');
    return false;
  }
}

/**
 * Handle premium purchase with user feedback using RevenueCat.
 */
export async function handlePurchasePremium(): Promise<boolean> {
  if (!arePurchasesAvailable()) {
    toast.info('Purchases are not available on this platform.');
    return false;
  }

  try {
    console.log('Attempting to purchase premium with RevenueCat...');
    toast.loading('Fetching available plans...', { id: 'purchase-premium' });

    const offering: PurchasesOffering | null = await getOfferings();
    if (!offering) {
      toast.dismiss('purchase-premium');
      toast.error('No subscription plans available at the moment. Please try again later.');
      console.error('RevenueCat: No offerings found.');
      return false;
    }

    // Find the specific package for PREMIUM_PRODUCT_ID (e.g., 'rc_0002')
    // The packageIdentifier in findPackageByIdentifier might be 'default', or your specific package ID.
    // We are using product ID directly in findPackageByIdentifier now.
    const premiumPackage: PurchasesPackage | null = findPackageByIdentifier(offering);

    if (!premiumPackage) {
      toast.dismiss('purchase-premium');
      toast.error('The desired subscription plan was not found. Please contact support.');
      console.error(`RevenueCat: Package for product ID ${PREMIUM_PRODUCT_ID} not found in current offering.`);
      return false;
    }

    toast.loading('Processing purchase...', { id: 'purchase-premium' });
    const purchaseResult = await RCatPurchasePackage(premiumPackage);
    toast.dismiss('purchase-premium');

    if (purchaseResult) {
      const { customerInfo } = purchaseResult;
      if (customerInfo.entitlements.active['Pro']) {
        toast.success('Welcome to Pro! 🎉');
        console.log('RevenueCat: Pro purchase successful.');
        // Potentially navigate or update UI state
        // window.history.back();
        return true;
      } else {
        toast.warning('Purchase completed, but Pro entitlement is not yet active. Please check again shortly.');
        console.warn('RevenueCat: Purchase completed, but Pro entitlement not immediately active.', customerInfo);
        return false; // Or true if purchase itself was successful but entitlement is pending
      }
    } else {
      // Error or cancellation is handled and logged within RCatPurchasePackage (revenuecat.ts)
      // Toast for cancellation is already handled there if userCancelled is true.
      // If it's an error, it's logged. We might not need another toast here unless it's generic.
      console.log('RevenueCat: Purchase did not complete (cancelled or error).');
      return false;
    }
  } catch (error) { // This catch is for unexpected errors in this handler itself
    console.error('Failed to purchase premium (RevenueCat):', error);
    toast.dismiss('purchase-premium');
    toast.error('An unexpected error occurred during purchase. Please try again.');
    return false;
  }
}

/**
 * Check if user currently has the "Pro" entitlement using RevenueCat.
 */
export async function isPremiumUser(): Promise<boolean> {
  try {
    return await RCatIsProActive();
  } catch (error) {
    console.error('Failed to check premium status (RevenueCat):', error);
    return false;
  }
}

/**
 * Get user's subscription status for UI display using RevenueCat.
 */
export async function getSubscriptionStatus(): Promise<{
  isPremium: boolean;
  tier: 'free' | 'Pro'; // Updated to 'Pro'
  expirationDate?: string | null; // RevenueCat provides this
  productId?: string | null;
  willRenew?: boolean;
}> {
  try {
    const customerInfo = await RCatGetCustomerInfo();
    if (!customerInfo) {
      return { isPremium: false, tier: 'free' };
    }

    const proEntitlement = customerInfo.entitlements.active['Pro'];
    const isPro = proEntitlement !== undefined;

    return {
      isPremium: isPro,
      tier: isPro ? 'Pro' : 'free',
      expirationDate: proEntitlement?.expirationDate || null,
      productId: proEntitlement?.productIdentifier || null,
      willRenew: proEntitlement?.willRenew ?? null, // Use nullish coalescing for boolean
    };
  } catch (error) {
    console.error('Failed to get subscription status (RevenueCat):', error);
    return {
      isPremium: false,
      tier: 'free',
    };
  }
}

/**
 * Check if purchases are available (device/platform support).
 * RevenueCat handles most platform checks internally.
 * This can be simplified or enhanced with Capacitor checks.
 */
export function arePurchasesAvailable(): boolean {
  if (!Capacitor.isNativePlatform()) {
    console.log("Purchases might not be available: Not a native platform.");
    // For web, RevenueCat SDK might not fully function for purchases
    // depending on Stripe integration etc. For now, assume not available for non-native.
    return false;
  }
  // Add any other platform-specific checks if necessary
  return true;
}