import React, { useEffect } from 'react';

const CustomSplashScreen: React.FC = () => {
  // Preload critical images during splash screen
  useEffect(() => {
    const imagesToPreload = [
      '/icons/header_logo.webp',
      '/mainscreen.webp',
      '/icons/icon-192.webp',
      '/icons/icon-512.webp',
      // Add other critical images here
    ];

    imagesToPreload.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  }, []);
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50" style={{ backgroundColor: '#f0bbcc' }}>
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 0.3; transform: scale(0.5); }
          50% { opacity: 1; transform: scale(1); }
        }
        
        .loading-ring {
          animation: spin 1s linear infinite;
        }
        
        .pulse-dot {
          animation: pulse 1.5s ease-in-out infinite;
        }
        
        .pulse-dot:nth-child(1) { animation-delay: 0s; }
        .pulse-dot:nth-child(2) { animation-delay: 0.2s; }
        .pulse-dot:nth-child(3) { animation-delay: 0.4s; }
        .pulse-dot:nth-child(4) { animation-delay: 0.6s; }
        .pulse-dot:nth-child(5) { animation-delay: 0.8s; }
        .pulse-dot:nth-child(6) { animation-delay: 1s; }
        .pulse-dot:nth-child(7) { animation-delay: 1.2s; }
        .pulse-dot:nth-child(8) { animation-delay: 1.4s; }
      `}</style>
      
      {/* Logo in center */}
      <div className="relative flex flex-col items-center">
        <img 
          src="/icons/header_logo.webp" 
          alt="Chatbuster Logo" 
          className="w-48 h-24 object-contain mb-4"
        />
        
        {/* Loading animation ring */}
        <div className="relative">
          <div className="loading-ring w-16 h-16 border-4 border-white border-opacity-30 rounded-full border-t-white" />
          
          {/* Rotating dots around the ring */}
          <div className="loading-ring absolute inset-0">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="absolute w-2 h-2 bg-white rounded-full"
                style={{
                  top: '50%',
                  left: '50%',
                  transform: `translate(-50%, -50%) rotate(${i * 20}deg) translateY(-32px)`,
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomSplashScreen;