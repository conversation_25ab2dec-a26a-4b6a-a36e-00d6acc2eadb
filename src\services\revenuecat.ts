// revenuecat.ts
// Service for interacting with the RevenueCat SDK

import { Purchases, LOG_LEVEL, PurchasesOffering, PurchasesPackage, CustomerInfo, PurchasesError, PRODUCT_CATEGORY } from '@revenuecat/purchases-capacitor';
import { Capacitor } from '@capacitor/core';

// --- Constants ---
// Import enhanced environment detection
import { isProductionMode, getEnvironmentInfo } from '@/utils/environment';

// RevenueCat API Keys (same for sandbox and production)
// RevenueCat automatically detects sandbox vs production based on app build and App Store environment
const REVENUECAT_API_KEY_IOS = 'appl_VCKhwsbdVqqRMYBtVrAYirJVUyJ';
const REVENUECAT_API_KEY_ANDROID = 'goog_SAadaCCBaxxsfjgiNFpawHuvWpw';

const ENTITLEMENT_ID_PRO = 'Pro'; // As per your setup

// --- State ---
let isRevenueCatConfigured = false;
let currentAppUserId: string | null = null;

// --- Initialization and Configuration ---

/**
 * Initializes the RevenueCat SDK with anonymous user support.
 * Call this once when your app starts.
 * @param appUserId Optional user identifier. If null, uses anonymous user.
 */
export const initializeRevenueCat = async (appUserId?: string | null): Promise<void> => {
  if (isRevenueCatConfigured) {
    console.log('RevenueCat already configured.');
    // If appUserId changed, log in the new user
    if (appUserId && appUserId !== currentAppUserId) {
      await login(appUserId);
    }
    return;
  }

  // Select API key based on platform (same key works for both sandbox and production)
  const apiKey = Capacitor.getPlatform() === 'ios' ? REVENUECAT_API_KEY_IOS : REVENUECAT_API_KEY_ANDROID;

  // Log configuration - RevenueCat automatically detects sandbox vs production
  const envInfo = getEnvironmentInfo();
  console.log('💳 RevenueCat Configuration:', {
    isProductionMode: isProductionMode(),
    platform: Capacitor.getPlatform(),
    environmentInfo: envInfo,
    note: 'RevenueCat automatically detects sandbox vs production based on app build'
  });

  // Show helpful RevenueCat info in development
  if (!isProductionMode()) {
    console.log('🔧 RevenueCat Development Info:');
    console.log('   • Sandbox mode will be used automatically for development builds');
    console.log('   • Production mode will be used automatically for production builds');
    if (envInfo.source === 'url') {
      console.log('   • Environment mode overridden by URL parameter');
    }
  }

  if (!apiKey.startsWith('appl_') && !apiKey.startsWith('goog_')) {
    console.warn('RevenueCat API key looks incorrect. Please check your configuration.');
    // Optionally, prevent initialization if keys are clearly placeholders
    // return;
  }

  try {
    // Purchases.setDebugLogsEnabled(true); // Enable for development
    Purchases.setLogLevel({ level: LOG_LEVEL.DEBUG }); // More detailed logs

    // Configure Purchases SDK - use null for anonymous users
    await Purchases.configure({
      apiKey: apiKey,
      appUserID: appUserId || null, // Use null for anonymous users to get RevenueCat anonymous ID
      // observerMode: false, // Set to true if you are using your own purchase infrastructure
      // usesStoreKit2IfAvailable: true, // For iOS, defaults to true
    });

    // Get the actual user ID that RevenueCat assigned (could be anonymous)
    const actualUserId = await getAppUserID();
    currentAppUserId = actualUserId;
    isRevenueCatConfigured = true;
    
    console.log('RevenueCat SDK configured successfully');
    console.log('Requested user ID:', appUserId || 'anonymous');
    console.log('Actual RevenueCat user ID:', actualUserId);

    // Add a listener for purchaser info updates
    Purchases.addCustomerInfoUpdateListener(async (info: CustomerInfo) => {
      console.log('CustomerInfo updated:', info);
      // TODO: Handle updated customer info (e.g., update UI, grant/revoke access)
      // This is crucial for reacting to subscription changes that happen outside direct app interaction
      // (e.g., renewals, cancellations from store settings)
      const isActive = await isProActive();
      console.log('Pro entitlement active after update listener:', isActive);
      // You might want to dispatch an event or call a state update function here
    });

  } catch (e) {
    const error = e as PurchasesError;
    console.error('Error configuring RevenueCat SDK:', error.message, error.underlyingErrorMessage);
    isRevenueCatConfigured = false;
  }
};

/**
 * Logs in a user with RevenueCat.
 * This is useful if you passed null for appUserID during configuration or if the user logs in later.
 * @param appUserId The unique identifier for the user.
 */
export const login = async (appUserId: string): Promise<CustomerInfo | null> => {
  if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, login skipped.');
    // For web, you might return a mock or throw an error if purchases are expected
    return null;
  }
  if (!appUserId) {
    console.error('Cannot login with null or empty appUserId');
    return null;
  }

  try {
    const { customerInfo, created } = await Purchases.logIn({ appUserID: appUserId });
    currentAppUserId = appUserId;
    console.log(created ? 'User logged in and created in RevenueCat.' : 'User logged in to RevenueCat.', customerInfo);
    return customerInfo;
  } catch (e) {
    const error = e as PurchasesError;
    console.error('Error logging in to RevenueCat:', error.message, error.underlyingErrorMessage);
    return null;
  }
};

/**
 * Logs out the current user from RevenueCat.
 */
export const logout = async (): Promise<CustomerInfo | null> => {
  if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, logout skipped.');
    return null;
  }
  try {
    const { customerInfo } = await Purchases.logOut();
    currentAppUserId = null;
    console.log('User logged out from RevenueCat.', customerInfo);
    return customerInfo;
  } catch (e) {
    const error = e as PurchasesError;
    console.error('Error logging out from RevenueCat:', error.message, error.underlyingErrorMessage);
    return null;
  }
};

// --- Offerings and Products ---

/**
 * Fetches the current offerings from RevenueCat.
 */
export const getOfferings = async (): Promise<PurchasesOffering | null> => {
  if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, getOfferings skipped.');
    return null;
  }
  try {
    const offerings = await Purchases.getOfferings();
    if (offerings.current) {
      console.log('Current offering:', offerings.current);
      // offerings.current.availablePackages.forEach(pkg => {
      //   console.log('Package:', pkg.identifier, pkg.product.title, pkg.product.priceString);
      // });
      return offerings.current;
    }
    console.log('No current offering found.');
    return null;
  } catch (e) {
    const error = e as PurchasesError;
    console.error('Error fetching offerings:', error.message, error.underlyingErrorMessage);
    return null;
  }
};

// --- Purchasing ---

/**
 * Purchases a package (product).
 * @param packageToPurchase The RevenueCat package to purchase.
 */
export const purchasePackage = async (packageToPurchase: PurchasesPackage): Promise<{ customerInfo: CustomerInfo; productIdentifier: string } | null> => {
  if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, purchasePackage skipped.');
    return null;
  }
  try {
    console.log('Attempting to purchase package:', packageToPurchase.identifier, packageToPurchase.product.identifier);
    const { customerInfo, productIdentifier } = await Purchases.purchasePackage({ aPackage: packageToPurchase });
    console.log(`Purchase successful for product ${productIdentifier}`, customerInfo);
    // Check entitlements after purchase
    if (customerInfo.entitlements.active[ENTITLEMENT_ID_PRO]) {
      console.log('User now has Pro entitlement!');
    }
    return { customerInfo, productIdentifier };
  } catch (e) {
    const error = e as PurchasesError;
    if (!error.userCancelled) {
      console.error('Error purchasing package:', error.message, error.underlyingErrorMessage, error.code, error.userCancelled);
    } else {
      console.log('User cancelled purchase flow.');
    }
    return null;
  }
};

/**
 * Restores purchases for the current user.
 */
export const restorePurchases = async (): Promise<CustomerInfo | null> => {
  if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, restorePurchases skipped.');
    return null;
  }
  try {
    const { customerInfo } = await Purchases.restorePurchases();
    console.log('Purchases restored successfully.', customerInfo);
    if (customerInfo.entitlements.active[ENTITLEMENT_ID_PRO]) {
      console.log('User has active Pro entitlement after restore.');
    } else {
      console.log('No active Pro entitlement found after restore.');
    }
    return customerInfo;
  } catch (e) {
    const error = e as PurchasesError;
    console.error('Error restoring purchases:', error.message, error.underlyingErrorMessage);
    return null;
  }
};

// --- Entitlements and Customer Info ---

/**
 * Checks if the 'Pro' entitlement is active for the current user.
 */
export const isProActive = async (): Promise<boolean> => {
  if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, isProActive check skipped.');
    return false; // Default to not active if SDK not ready or on web without mock
  }
  try {
    const { customerInfo } = await Purchases.getCustomerInfo();
    // console.log('Customer Info for isProActive check:', customerInfo);
    if (typeof customerInfo.entitlements.active[ENTITLEMENT_ID_PRO] !== "undefined") {
      console.log(`'${ENTITLEMENT_ID_PRO}' entitlement is active.`);
      return true;
    } else {
      console.log(`'${ENTITLEMENT_ID_PRO}' entitlement is NOT active.`);
      return false;
    }
  } catch (e) {
    const error = e as PurchasesError;
    console.error('Error checking "Pro" status:', error.message, error.underlyingErrorMessage);
    return false;
  }
};

/**
 * Gets the current CustomerInfo.
 */
export const getCustomerInfo = async (): Promise<CustomerInfo | null> => {
   if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, getCustomerInfo skipped.');
    return null;
  }
  try {
    const { customerInfo } = await Purchases.getCustomerInfo();
    console.log('Current CustomerInfo:', customerInfo);
    return customerInfo;
  } catch (e) {
    const error = e as PurchasesError;
    console.error('Error fetching customer info:', error.message, error.underlyingErrorMessage);
    return null;
  }
};

/**
 * Gets the current App User ID being used by RevenueCat.
 * This will be the anonymous ID if no custom user ID was provided.
 */
export const getAppUserID = async (): Promise<string> => {
  if (!isRevenueCatConfigured && !Capacitor.isNativePlatform()) {
    console.warn('RevenueCat not configured or not native platform, getAppUserID skipped.');
    return currentAppUserId || ''; // Fallback or handle as error
  }
  try {
    const { appUserID } = await Purchases.getAppUserID();
    // Update our cached current user ID
    currentAppUserId = appUserID;
    return appUserID;
  } catch (e) {
     console.error('Error getting App User ID:', e);
     return currentAppUserId || ''; // Fallback
  }
};

/**
 * Gets the RevenueCat user ID to use as the primary user identifier.
 * This replaces the old UUID-based device ID system.
 * @returns The RevenueCat user ID (anonymous or custom)
 */
export const getRevenueCatUserId = async (): Promise<string> => {
  try {
    // If RevenueCat is configured, get the user ID from there
    if (isRevenueCatConfigured || Capacitor.isNativePlatform()) {
      const userId = await getAppUserID();
      if (userId) {
        console.log('Using RevenueCat user ID:', userId.substring(0, 8) + '...');
        return userId;
      }
    }

    // Fallback: if RevenueCat not available, generate a temporary UUID
    // This should only happen in development or web environments
    console.warn('RevenueCat not available, using fallback UUID generation');
    const fallbackId = generateFallbackUserId();
    return fallbackId;
  } catch (error) {
    console.error('Error getting RevenueCat user ID:', error);
    // Generate fallback ID in case of error
    const fallbackId = generateFallbackUserId();
    console.warn('Using fallback user ID due to error:', fallbackId.substring(0, 8) + '...');
    return fallbackId;
  }
};

/**
 * Generates a fallback user ID when RevenueCat is not available.
 * Uses localStorage to persist the ID across sessions.
 */
const generateFallbackUserId = (): string => {
  const FALLBACK_USER_ID_KEY = 'chat_fallback_user_id';
  
  let fallbackId = localStorage.getItem(FALLBACK_USER_ID_KEY);
  
  if (!fallbackId) {
    // Generate a new fallback ID with a prefix to distinguish it
    fallbackId = 'fallback_' + crypto.randomUUID();
    localStorage.setItem(FALLBACK_USER_ID_KEY, fallbackId);
    console.log('Generated new fallback user ID');
  }
  
  return fallbackId;
};

// --- Helper to get specific package ---
/**
 * Finds a specific package (e.g., your 'rc_0002' product) from the current offering.
 * @param offering The current PurchasesOffering.
 * @param packageIdentifier The identifier of the package to find (e.g., 'default', 'monthly', or your specific setup).
 *                          Often, if you have one product per offering, it might be the 'default' or only package.
 *                          Or, if your product ID `rc_0002` is directly a package identifier in RC.
 */
export const findPackageByIdentifier = (
  offering: PurchasesOffering | null,
  packageIdentifier: string = "default" // RevenueCat often uses 'default' for the main package in an offering.
                                         // Or this could be your specific product ID if it's set as a package ID.
): PurchasesPackage | null => {
  if (!offering) return null;

  // Option 1: If your product ID `rc_0002` is the package identifier
  // let pkg = offering.availablePackages.find(p => p.identifier === packageIdentifier);
  // if (pkg) return pkg;

  // Option 2: Find package by product ID (e.g. 'rc_0002')
  // This is more robust if package identifiers are generic like 'default', 'monthly'
  const targetProductId = 'rc_0002'; // Your specific product ID
  let productPkg = offering.availablePackages.find(p => p.product.identifier === targetProductId);
  if (productPkg) return productPkg;


  // Fallback: Try common identifiers or the first package if specific one not found
  if (packageIdentifier === "default" && offering.availablePackages.length > 0) {
     // Check if there's a package explicitly named "default" or similar common terms
     const defaultPkg = offering.availablePackages.find(p => p.identifier.toLowerCase() === "default");
     if (defaultPkg) return defaultPkg;
  }
  
  // If only one package, assume it's the one
  if (offering.availablePackages.length === 1) {
    return offering.availablePackages[0];
  }
  
  console.warn(`Package with identifier '${packageIdentifier}' or product ID '${targetProductId}' not found in current offering. Available packages:`, offering.availablePackages.map(p => `${p.identifier} (product: ${p.product.identifier})`));
  return null; // Or return offering.monthly, offering.annual etc. if you use those.
};


// TODO: Add your RevenueCat Public API Keys for iOS and Android above.
// TODO: Ensure the ENTITLEMENT_ID_PRO ('Pro') matches your RevenueCat setup.
// TODO: Implement UI updates in response to CustomerInfo updates (listener).