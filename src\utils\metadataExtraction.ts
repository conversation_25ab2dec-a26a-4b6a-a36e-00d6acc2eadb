// Helper functions for metadata extraction from chat text

export const extractMessageCount = (text: string): number => {
  if (!text) return 0;
  const lines = text.split('\n');
  return lines.filter(line => line.includes(':')).length;
};

export const extractParticipantCount = (text: string): number => {
  if (!text) return 0;
  const participantSet = new Set<string>();
  const lines = text.split('\n');
  const messageLines = lines.filter(line => line.includes(':'));
  
  messageLines.forEach(line => {
    const match = line.match(/^([^:]+):/);
    if (match && match[1]) {
      participantSet.add(match[1].trim());
    }
  });
  
  return participantSet.size;
};