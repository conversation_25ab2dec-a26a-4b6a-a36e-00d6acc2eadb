{"appId": "app.chatvibeanalyzer.mobile", "appName": "Chatbuster", "webDir": "dist", "server": {"url": "http://192.168.2.34:3000", "cleartext": true, "androidScheme": "https"}, "plugins": {"SplashScreen": {"launchShowDuration": 2000, "launchAutoHide": false, "launchFadeOutDuration": 300, "backgroundColor": "#f0bbcc", "androidSplashResourceName": "splash", "androidScaleType": "FIT_CENTER", "showSpinner": false, "androidSpinnerStyle": "large", "iosSpinnerStyle": "small", "spinnerColor": "#999999", "splashFullScreen": true, "splashImmersive": true, "layoutName": "launch_screen", "useDialog": false}, "LiveUpdates": {"enabled": true, "autoUpdateEnabled": true}, "ScreenOrientation": {"lockOrientation": "portrait"}, "FileOpener": {"associations": [{"ext": "txt", "mimeType": "text/plain"}, {"ext": "zip", "mimeType": "application/zip"}]}, "App": {"url": "chatvibeanalyzer://", "allowRedirect": true}, "Filesystem": {"readChunkSize": 1048576}, "PurchasesCapacitor": {"android": {"enablePlayStoreIntegration": true, "enablePlayStoreObserverMode": false}}}, "ios": {"contentInset": "never", "scheme": "App", "handleApplicationNotifications": true}, "packageClassList": ["AppPlugin", "CAPBrowserPlugin", "FilesystemPlugin", "HapticsPlugin", "PreferencesPlugin", "ScreenOrientationPlugin", "SplashScreenPlugin", "FileOpenerPlugin", "PurchasesPlugin", "SentryCapacitor"]}