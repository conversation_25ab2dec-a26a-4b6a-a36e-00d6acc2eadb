/**
 * API configuration
 */

// Backend API URL configuration
export const API_CONFIG = {
  PRODUCTION_URL: 'https://chatbuster-backend.vibalyze.workers.dev',
  LOCALHOST_URL: 'http://localhost:8787',
  USE_LOCALHOST: false
};

// Helper function to check if the device is mobile
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Determine which API URL to use
export const getApiUrl = () => {
  return isMobileDevice() || process.env.NODE_ENV === 'production' || !API_CONFIG.USE_LOCALHOST
    ? API_CONFIG.PRODUCTION_URL
    : API_CONFIG.LOCALHOST_URL;
};