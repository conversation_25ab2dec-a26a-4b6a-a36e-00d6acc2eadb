import { demoData } from '@/data/analysisData';
import { AnalysisData } from '@/contexts/AnalysisContext';
import { getDeviceId } from './device';
import { canAnalyze } from './quota';
import { saveAnalysis } from '../utils/analysisStorage';
import { isProductionMode, isDevelopmentMode, getEnvironmentInfo, logEnvironmentInfo } from '@/utils/environment';
import { toast } from '@/utils/toast';
import { hapticFeedback } from '@/utils/haptics';
import { getApiUrl } from '../config/api';

// Get API URL from centralized config
const API_URL = getApiUrl();

// Helper function to check if the device is mobile
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Log the API URL configuration with enhanced environment info
const apiConfig = {
  isMobile: isMobileDevice(),
  buildMode: process.env.NODE_ENV,
  isProductionMode: isProductionMode(),
  isDevelopmentMode: isDevelopmentMode(),
  apiUrl: API_URL,
  environmentInfo: getEnvironmentInfo()
};

console.log('🚀 API Configuration:', apiConfig);

// Log detailed environment info in development mode
logEnvironmentInfo();

// Show helpful API info in development
if (isDevelopmentMode()) {
  console.log('🔧 API Development Info:');
  console.log('   • Backend URL:', API_URL);
  console.log('   • Mobile device:', isMobileDevice() ? 'Yes' : 'No');
  if (apiConfig.environmentInfo.source === 'url') {
    console.log('   • Environment overridden by URL parameter');
  }
}

/**
 * Analyzes chat text by sending it to the backend API
 * @param text The chat text to analyze
 * @param fileName Optional filename to send to the backend
 * @param analysisType Optional analysis type ('group' or 'love')
 * @returns The analysis result
 */
export const analyzeChatText = async (text: string, fileName: string = 'chat.txt', analysisType: 'group' | 'love' = 'group'): Promise<AnalysisData> => {
  try {
    console.log('Analyzing chat text, length:', text.length, 'fileName:', fileName);

    // Validate input
    // Validate input - no mock data fallback in production
    if (!text || text.trim().length === 0) {
      if (isDevelopmentMode() && !isMobileDevice()) {
        console.warn('Empty text provided for analysis, using mock data');
        toast.dev('No text provided - using demo data for development');
        return {
          source: 'mock',
          fileName: fileName,
          text: text || '',
          result: demoData
        };
      }
      throw new Error('Please provide chat text for analysis');
    }

    // Check quota before proceeding with analysis
    const quotaCheck = await canAnalyze();
    if (!quotaCheck.allowed) {
      console.log('Quota check failed:', quotaCheck.message);
      throw new Error(quotaCheck.message || 'Analysis limit reached');
    }

    console.log('Quota check passed, proceeding with analysis');

    // Log API connection info for debugging
    console.log('API connection info:', {
      apiUrl: API_URL,
      networkStatus: navigator.onLine ? 'online' : 'offline',
      isMobile: isMobileDevice()
    });

    // Use FormData instead of JSON to match backend expectations
    console.log('Sending text submission as FormData with analysis type:', analysisType);
    const formData = new FormData();

    // Create a file from the text
    const file = new File([text], fileName, { type: 'text/plain' });
    formData.append('chatfile', file);

    // Add the analysis type to the form data
    formData.append('analysisType', analysisType);

    // Log the first 200 characters of the text being sent
    console.log('Text sample being sent (first 200 chars):', text.substring(0, 200));

    // Get device ID for authentication (now RevenueCat anonymous ID)
    const deviceId = await getDeviceId();
    console.log(`Sending request to ${API_URL}/submit with RevenueCat user ID: ${deviceId.substring(0, 8)}...`);
    
    const response = await fetch(`${API_URL}/submit`, {
      method: 'POST',
      body: formData,
      mode: 'cors',
      credentials: 'omit',
      headers: {
        'Authorization': `Bearer ${deviceId}`,
        'Accept': 'application/json'
        // No Content-Type header - browser will set it with boundary for FormData
      }
    });

    console.log('Response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status} ${response.statusText}`, errorText);

      // Try to parse the error response as JSON
      let errorJson = null;
      try {
        errorJson = JSON.parse(errorText);
        console.log('Parsed error JSON:', errorJson);
      } catch (e) {
        console.log('Error response is not valid JSON');
      }

      // For 400 errors (quota exceeded), throw SubscriptionRequiredError consistently
      if (response.status === 400) {
        console.log('Received 400 error from API (quota exceeded)');
        
        // Extract the error message from the response
        const message = errorJson?.error || errorText || 'Analysis quota exceeded';
        
        // Only haptic feedback, no toast - subscription modal will handle UI
        hapticFeedback.quotaExceeded();
        
        // Throw a specific error type for quota issues - consistent with pre-API quota check
        const error = new Error(message);
        error.name = 'SubscriptionRequiredError';
        throw error;
      }

      // For 429 errors, handle quota exceeded scenarios
      if (response.status === 429) {
        console.log('Received 429 error from API - quota exceeded');
        
        // Extract the error message from the response
        const message = errorJson?.error || errorText || 'Analysis quota exceeded';
        console.log('🔍 [API] 429 error message extracted:', message);
        
        // 429 errors are quota/rate limit related, so always throw SubscriptionRequiredError
        console.log('429 error detected, throwing SubscriptionRequiredError');
        hapticFeedback.quotaExceeded();
        
        const error = new Error(message);
        error.name = 'SubscriptionRequiredError';
        console.log('🔍 [API] Created SubscriptionRequiredError with message:', error.message);
        throw error;
      }

      // For 500 errors, which now indicate a parsing failure, throw error for dialog handling
      if (response.status === 500) {
        hapticFeedback.analysisError();
        const error = new Error("Something went wrong while processing your chat file. Please ensure it is a valid WhatsApp export and try again.");
        error.name = 'ApiError';
        throw error;
      }

      throw new Error(`API error: ${response.status} ${response.statusText}\n${errorText}`);
    }

    const result = await response.json();
    console.log('API response received:', result ? 'success' : 'empty');

    // If there's an error in the result, throw it
    if (result.error) {
      console.error('API returned error:', result.error);
      throw new Error(result.error);
    }

    // If the result is empty or invalid, throw an error
    if (!result || typeof result !== 'object') {
      console.error('API returned invalid result:', result);
      throw new Error('Invalid response from API');
    }

    console.log('Analysis successful');

    // Check if the result has all the expected data
    // If not, merge with mock data to ensure all components have data to display
    const mergedResult = {
      ...demoData,  // Start with mock data as base
      ...result,    // Override with actual API data where available
    };

    // Check if critical sections exist and use mock data as fallback
    const criticalSections = [
      'personStats', 'dashboardChatStats', 'charts', 'dataMetrics', 'emotionMetrics',
      'wittyMetrics', 'chatStats', 'toxicityParticipants', 'groupSlang', 'topDomains',
      'dominantTopics', 'slangCloud', 'commonPhrases', 'frenemyDynamics', 'crewRankings',
      'respectScores', 'socialGraphData',
      // New data types
      'cloutPulseData', 'cliqueExclusionData', 'loverDynamicsData', 'memberComparisonData',
      'chatHighlights', 'quiz', 'compatibilityMetrics'
    ];
    let usedMockData = false;

    criticalSections.forEach(section => {
      if (!result[section]) {
        console.log(`Missing ${section} in API response, using mock data`);
        mergedResult[section] = demoData[section];
        usedMockData = true;
      } else {
        console.log(`Found ${section} in API response`);
      }
    });

    // Special handling for compatibilityMetrics - ensure proper structure for nicknames and inside jokes
    if (mergedResult.compatibilityMetrics) {
      console.log('Processing compatibilityMetrics for proper structure');

      // Ensure it's an array
      if (!Array.isArray(mergedResult.compatibilityMetrics)) {
        mergedResult.compatibilityMetrics = [mergedResult.compatibilityMetrics];
      }

      // Validate and enhance each compatibility metric
      mergedResult.compatibilityMetrics = mergedResult.compatibilityMetrics.map((item: any, index: number) => {
        if (!item || typeof item !== 'object') {
          console.warn(`Invalid compatibilityMetrics item at index ${index}:`, item);
          return null;
        }

        // Ensure required fields exist
        const enhancedItem = {
          ...item,
          pair: item.pair || [`Person ${index * 2 + 1}`, `Person ${index * 2 + 2}`],
          compatibility_score: typeof item.compatibility_score === 'number' ? item.compatibility_score : 75,
          relationship_title: item.relationship_title || 'Dynamic Duo',
          metrics: item.metrics || {},
          // Handle different possible field names for inside jokes - check both top level and inside metrics
          insight_jokes: item.insight_jokes ||
                        item.inside_jokes ||
                        item.jokes ||
                        item.metrics?.insight_jokes ||
                        item.metrics?.inside_jokes ||
                        'No inside jokes found yet.',
          // Handle different possible field names for nicknames - check both top level and inside metrics
          nicknames: item.nicknames ||
                    item.reference_terms ||
                    item.terms ||
                    item.metrics?.nicknames ||
                    item.metrics?.reference_terms ||
                    'No special nicknames used.',
          summary: item.summary || `${item.pair?.[0] || 'Person 1'} and ${item.pair?.[1] || 'Person 2'} have a unique relationship dynamic.`
        };

        console.log(`Enhanced compatibilityMetrics item ${index}:`, {
          pair: enhancedItem.pair,
          insight_jokes: enhancedItem.insight_jokes,
          nicknames: enhancedItem.nicknames
        });

        return enhancedItem;
      }).filter(Boolean); // Remove any null items
    }

    // Special handling for topDomains - convert from backend format to frontend format if needed
    if (mergedResult.topDomains && mergedResult.topDomains.length > 0 && mergedResult.topDomains[0]?.domain) {
      console.log('Converting topDomains from backend format to frontend format in API service');
      mergedResult.topDomains = mergedResult.topDomains.map((item: any) => ({
        name: item.domain,
        count: item.count
      }));
    }

    // Check if topDomains exists in groupSummary
    if (mergedResult.groupSummary?.topDomains && mergedResult.groupSummary.topDomains.length > 0) {
      console.log('Found topDomains in groupSummary, extracting and converting to frontend format in API service');
      mergedResult.topDomains = mergedResult.groupSummary.topDomains.map((item: any) => ({
        name: item.domain,
        count: item.count
      }));
    }

    // Make sure totalLinks is available for the LinkCentral component
    if (!mergedResult.totalLinks && mergedResult.dashboardChatStats?.totalLinks) {
      console.log('Using totalLinks from dashboardChatStats');
      mergedResult.totalLinks = mergedResult.dashboardChatStats.totalLinks;
    }

    // Also check if totalLinks exists in groupSummary
    if (!mergedResult.totalLinks && mergedResult.groupSummary?.totalLinks) {
      console.log('Using totalLinks from groupSummary');
      mergedResult.totalLinks = mergedResult.groupSummary.totalLinks;
    }

    // Handle slangCloud from languageAnalysis
    if (mergedResult.languageAnalysis?.slangCloud && mergedResult.languageAnalysis.slangCloud.length > 0) {
      console.log('Found slangCloud in languageAnalysis, extracting and converting to frontend format in API service');
      mergedResult.slangCloud = mergedResult.languageAnalysis.slangCloud.map((item: any) => ({
        term: item.text,
        count: item.value,
        definition: `Used ${item.value}x in your chat conversations`
      }));
    }

    // Extract mostLikelyTo from premiumFeatures if available
    if (mergedResult.premiumFeatures?.mostLikelyTo && Array.isArray(mergedResult.premiumFeatures.mostLikelyTo) && mergedResult.premiumFeatures.mostLikelyTo.length > 0) {
      console.log('Found mostLikelyTo in premiumFeatures, extracting to root level for easier access');
      mergedResult.mostLikelyTo = mergedResult.premiumFeatures.mostLikelyTo;
    }

    // Add a flag to indicate this is merged data
    mergedResult._isMergedWithMockData = usedMockData;

    // Only log mock data usage in development
    if (isDevelopmentMode()) {
      console.log('Using merged data:', mergedResult._isMergedWithMockData ? 'Yes (some mock data used)' : 'No (all API data)');
      if (usedMockData) {
        toast.dev('Some sections using demo data for missing API fields');
      }
    }

    const apiResponse = {
      source: 'api' as 'api' | 'file' | 'mock',
      fileName: fileName,
      text: text,
      result: mergedResult,
      analysisType: analysisType, // Use the actual analysis type parameter passed to this function
      selectedChatType: (analysisType === 'love' ? 'one-on-one' : 'group') as 'one-on-one' | 'group' // Include selected chat type too with correct typing
    };

    console.log('API response includes analysisType:', apiResponse.analysisType);
    console.log('API response includes selectedChatType:', apiResponse.selectedChatType);

    return apiResponse;
  } catch (error) {
    console.error('Error analyzing chat text:', error);

    // Always re-throw SubscriptionRequiredError regardless of platform
    if (error.name === 'SubscriptionRequiredError') {
      throw error;
    }

    // For other errors, handle based on platform and production mode
    if (isMobileDevice() || isProductionMode()) {
      hapticFeedback.networkError();
      toast.network('Unable to analyze your chat right now. Please check your connection and try again.');
      throw error;
    } else {
      // On desktop/development only, fall back to mock data for non-quota errors
      hapticFeedback.analysisError();
      toast.dev('Error connecting to analysis server. Using demo data for development.');
      return {
        source: 'mock' as 'api' | 'file' | 'mock',
        fileName: fileName,
        text: text || '',
        result: demoData,
        analysisType: analysisType, // Use the actual analysis type parameter passed to this function
        selectedChatType: (analysisType === 'love' ? 'one-on-one' : 'group') as 'one-on-one' | 'group' // Include selected chat type too with correct typing
      };
    }
  }
};

/**
 * Analyzes a chat file by sending it to the backend API
 * @param file The chat file to analyze
 * @param analysisType Optional analysis type ('group' or 'love')
 * @returns The analysis result
 */
export const analyzeChatFile = async (file: File, analysisType: 'group' | 'love' = 'group'): Promise<AnalysisData> => {
  console.log('Analyzing chat file via analyzeChatText fallback:', file.name, 'size:', file.size);
  const fileText = await file.text();
  return analyzeChatText(fileText, file.name, analysisType);
};
